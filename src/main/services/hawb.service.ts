import { SqlQuerySpec } from '@azure/cosmos';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { AppLogger } from '~/utilities/logUtils.js';
import { UnitConverterUtils } from '~/utilities/unitConverterUtils.js';

import { Daos } from '~/daos/index.js';
import { IMerchantRateItemV2, IMerchantRateV2 } from '~/types/rate.type.js';

import CountryISOService from './countryISO-service.js';

const logger = new AppLogger({ name: 'HawbService' });

export type RateData = IMerchantRateV2 & { rateItems: IMerchantRateItemV2[] };

interface HawbUnit {
  weight: string;
  length: string;
  width: string;
  height: string;
}

interface HawbToComputePdAmount {
  id: string;
  mawb_no: string;
  destination_country: string;
  units: HawbUnit[];
}

export const HawbService = {
  async getHawbByMerchantName(
    merchantName: string,
    mawbId: string,
    fields: string[] | string = '*',
  ) {
    if (Array.isArray(fields)) {
      const query = fields.map((field) => `c.${field}`).join(', ');
      fields = query;
    }

    const querySpec: SqlQuerySpec = {
      query: `SELECT ${fields} FROM c WHERE c.merchant_name = @merchantName and c.mawb_id = @mawbId`,
      parameters: [
        {
          name: '@merchantName',
          value: merchantName,
        },
        {
          name: '@mawbId',
          value: mawbId,
        },
      ],
    };

    const [hawb] = await Daos.hawb.find(querySpec);
    logger.info({
      message: 'Hawb retrieved by merchant name and mawb ID successfully',
      merchantName,
      mawbId,
    });

    return hawb;
  },

  async updateHawbChargeableWeightAndPdAmount(encryptedMerchantNo: string, rateData: RateData) {
    // Get all valid hawbs to re-calculate their PD Amounts
    const hawbs = await this.getEligibleHawbs(encryptedMerchantNo);

    if (hawbs.length > 0) {
      this.validateRateDateRange(rateData);

      const payload = this.generateHawbsUpdatePayload(hawbs, rateData);
      const { successItems, errorItems } = await Daos.hawb.bulkUpdate(payload);

      for (const { item, error } of [...successItems, ...errorItems]) {
        let message = 'Update hawb successfully';

        if (error) {
          message = 'Update hawb error';
        }

        const dataForLogs = payload.find((hawb) => hawb.id === item.id);

        logger.info({ message, rateSheetId: rateData.id, dataForLogs, error });
      }

      return { successItems, errorItems };
    }

    logger.info({
      message: 'Calculate HAWB chargeable weight and PD B2B Amount',
      details: 'No hawb found',
      encryptedMerchantNo,
    });
  },

  async getEligibleHawbs(encryptedMerchantNumber: string) {
    const querySpec: SqlQuerySpec = {
      query: `SELECT
          c.id,
          c.mawb_no,
          c.destination_country,
          c.dimensions_unit,
          c.weight_unit,
          c.units
        FROM c
        WHERE c.merchant_account_number = @merchantAccountNo
        AND NOT IS_DEFINED(c.pd_invoice_no)
        AND ARRAY_CONTAINS(@statuses, ARRAY_SLICE(c.pd_invoicing_status, -1)[0].status)`,
      parameters: [
        {
          name: '@merchantAccountNo',
          value: encryptedMerchantNumber,
        },
        {
          name: '@statuses',
          value: [ENUM.invoiceStatus.PD_ready_to_invoice],
        },
      ],
    };

    const hawbs = await Daos.hawb.find<HawbToComputePdAmount>(querySpec);

    return hawbs;
  },

  validateRateDateRange(rateData: { validity_from: string; validity_to: string }) {
    const { validity_from: validFrom, validity_to: validTo } = rateData;
    const today = new Date();

    if (today < new Date(validFrom) || today > new Date(validTo)) {
      throw new Error(`Invalid date range. From: ${validFrom}. To: ${validTo}`);
    }
  },

  generateHawbsUpdatePayload(hawbs, rateData: RateData) {
    const payload = [];
    const {
      minimum_charge: minimumCharge,
      currency,
      rateItems,
      weight_unit: weightUnit,
      chargeable_weight: chargeType,
    } = rateData;

    for (const hawb of hawbs) {
      const { chargeableWeight, grossWeight, volumetricWeight } = this.computeChargeableWeight(
        hawb.units,
        hawb.weight_unit,
        hawb.dimensions_unit,
        weightUnit,
        chargeType,
      );
      const destinationCode = CountryISOService.getCountryCode2(
        AesUtils.CrtCounterDecrypt(hawb.destination_country),
      );
      const ratesByDestination = rateItems
        .filter((item) => item.country_code === destinationCode)
        .sort((a, b) => a.weight - b.weight);

      if (ratesByDestination.length === 0) {
        logger.warn({ message: 'No valid rates found for HAWB', hawbId: hawb.id });
        // eslint-disable-next-line no-continue
        continue;
      }

      // Assignment is true for both flat-rate type and weight-break type rates
      // For weight-break type: get the highest-weight rate by default
      let pdRate = ratesByDestination.at(-1).rate;

      // Weight-break type: if chargeable weight is in smaller weight range, use that upper-bound range rate
      if (ratesByDestination[0].weight) {
        for (const item of ratesByDestination) {
          if (chargeableWeight <= item.weight) {
            pdRate = item.rate;
            break;
          }
        }
      }

      const minChargeValue = minimumCharge[destinationCode];
      const pdAmount = Math.max(minChargeValue, chargeableWeight * pdRate);

      payload.push({
        id: hawb.id,
        partitionKey: hawb.mawb_no,
        pd_amount: pdAmount,
        pd_currency: currency,
        chargeable_weight: chargeableWeight,
        gross_weight: grossWeight,
        volumetric_weight: volumetricWeight,
      });
    }

    return payload;
  },

  computeChargeableWeight(
    hawbUnits: (HawbUnit & { weight_unit: string; dimensions_unit: string })[],
    hawbWeightUnit: string,
    hawbDimensionsUnit: string,
    rateWeightUnit: string,
    chargeType: string,
  ) {
    const DEFAULT_HAWB_WEIGHT_UNIT = 'kg';
    let chargeableWeight = 0;
    let grossWeight = 0;
    let volumetricWeight = 0;

    for (const unit of hawbUnits) {
      unit.weight_unit = hawbWeightUnit;
      unit.dimensions_unit = hawbDimensionsUnit;

      grossWeight += UnitConverterUtils.getGrossWeightKg(unit);
      volumetricWeight += UnitConverterUtils.getVolumetricWeightKg(unit);

      delete unit.weight_unit;
      delete unit.dimensions_unit;
    }

    if (rateWeightUnit !== DEFAULT_HAWB_WEIGHT_UNIT) {
      volumetricWeight = UnitConverterUtils.kgtolb(volumetricWeight);
      grossWeight = UnitConverterUtils.kgtolb(grossWeight);
    }

    switch (chargeType.toLowerCase()) {
      case ENUM.chargeType.GROSS_WEIGHT: {
        chargeableWeight = grossWeight;
        break;
      }

      case ENUM.chargeType.VOLUMETRIC_WEIGHT: {
        chargeableWeight = volumetricWeight;
        break;
      }

      default: {
        chargeableWeight = Math.max(grossWeight, volumetricWeight);
        break;
      }
    }

    return { chargeableWeight, grossWeight, volumetricWeight };
  },

  async getHawbById(hawbId: string, fields: string[] | string = '*') {
    if (Array.isArray(fields)) {
      const query = fields.map((field) => `c.${field}`).join(', ');
      fields = query;
    }

    const querySpec: SqlQuerySpec = {
      query: `SELECT ${fields} FROM c WHERE c.id = @hawbId`,
      parameters: [
        {
          name: '@hawbId',
          value: hawbId,
        },
      ],
    };

    const [hawb] = await Daos.hawb.find(querySpec);
    logger.info({
      message: 'HAWB retrieved by ID successfully',
      hawbId,
    });

    return hawb;
  },
};
