import { SqlQuerySpec } from '@azure/cosmos';

import axios from 'axios';
import { Parser as Json2csvParser } from 'json2csv';
import countBy from 'lodash/countBy.js';
import groupBy from 'lodash/groupBy.js';
import isEmpty from 'lodash/isEmpty.js';
import sortBy from 'lodash/sortBy.js';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';
import { genFwbPdf } from '~/models/genFwbPdf.js';
import { ReportUtils } from '~/models/reportUtils.js';

import CountryService from '~/services/countryISO-service.js';

import { AusPostUtils } from '~/utilities/ausPostUtils.js';
import { CommonUtils } from '~/utilities/commonUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { PlsReportUtils } from '~/utilities/plsReportUtils.js';
import { QueryUtils } from '~/utilities/queryUtils.js';
import { ShipmentUtils } from '~/utilities/shipmentUtils.js';
import { UnitConverterUtils } from '~/utilities/unitConverterUtils.js';
import { UpcUtils } from '~/utilities/upcUtils.js';

import { config } from '~/configs/index.js';
import { CONSTANTS } from '~/constants/index.js';
import { Daos } from '~/daos/index.js';

import { AzureStorageQueue } from '../common/azure/azure-storage-queue.js';
import { EXT } from '../types/ext.type.js';
import { ManifestItem, ParcelWithGroup } from '../types/manifest-item.type.js';
import { EnumMawbTranxType } from '../types/mawb.type.js';

import { ArchiverService } from './archiver.service.js';
import { AupostXlsService } from './aupost-xls-service.js';
import { CBService } from './cb.service.js';
import { CurrencyConversionService } from './currency-conversion-service.js';
import { EmailService } from './email.services.js';
import { GaylordServices } from './gaylord.service.js';
import { HawbService } from './hawb.service.js';
import { MawbService } from './mawb-service.js';
import { MerchantService } from './merchant-service.js';
import parcelHistoryService from './parcel-history.service.js';
import { StatusService } from './status_service.js';
import StatusMappingService from './statusMappingService.js';

const KERRY_CIF_THRESHOLD = 1500;
const HIGH_VALUE_MANIFEST_FILENAME = 'High value item manifest';
const LOW_VALUE_MANIFEST_FILENAME = 'Low value item manifest';
const XLSX_EXT = '.xlsx';

interface IPDInvoicingStatus {
  status: string;
  date: Date;
}

const logger = new AppLogger({ name: 'ManifestItemService' });

export const ManifestItemService = {
  async getParcelsByQuery(querySpec: SqlQuerySpec) {
    return Daos.manifest_items.find(querySpec);
  },

  async getSumOfAllSubtotalDeclaredCIFValueByTrackingId(tracking_id: string) {
    let sum = 0;
    const querySpec = {
      query: 'SELECT * FROM c WHERE c.tracking_id = @tracking_id',
      parameters: [
        {
          name: '@tracking_id',
          value: tracking_id,
        },
      ],
    };
    const collections = await Daos.manifest_items.find(querySpec);

    if (collections[0] && collections[0].item && collections[0].item.length > 0) {
      for (const i of collections[0].item) {
        if (i.total_declared_value) {
          sum += Number(i.total_declared_value);
        }
      }
    }

    return sum;
  },

  /**
   * query for all parcel that belong to a gaylord
   * @param {*} gaylordId
   * @returns list of parcel belong to inputed gaylord
   */
  async getParcelsByGlId(gaylordId: string, fields: any = '*') {
    if (Array.isArray(fields)) {
      const query = fields.map((field) => `c.${field}`);
      fields = query;
    }

    const querySpec = {
      query: `SELECT ${fields} FROM c WHERE c.gaylord_no = @gaylord_no`,
      parameters: [
        {
          name: '@gaylord_no',
          value: gaylordId,
        },
      ],
    };

    return Daos.manifest_items.find(querySpec);
  },

  async getAllFellowChildrenParcel(parentId: string, fields: any = '*') {
    if (Array.isArray(fields)) {
      const query = fields.map((field) => `c.${field}`);
      fields = query;
    }

    const querySpec = {
      query: `SELECT ${fields} FROM c WHERE c.parent_id = @parentId`,
      parameters: [
        {
          name: '@parentId',
          value: parentId,
        },
      ],
    };

    return Daos.manifest_items.find(querySpec);
  },

  async getParcelByIdOrTrackingId(idOrTrackingId) {
    const query = {
      query: 'SELECT c.id, c.tracking_status FROM c WHERE c.id = @id OR c.tracking_id = @id',
      parameters: [
        {
          name: '@id',
          value: idOrTrackingId,
        },
      ],
    };

    return Daos.manifest_items.find(query);
  },

  /**
   * Separate parcel from a mawb into group of below/above deminimis
   * @param allGLsInMawb
   * @param {*} allMerchant array of merchant
   * @param destinations
   * @returns parcels grouped into below/above deminimis
   */
  async parcelClassification(allGLsInMawb: any[], allMerchant: any[], destinations: any[]) {
    const destination = getDestByMawb(destinations, allGLsInMawb[0]);

    if (!destination) {
      throw new Error('Cannot get destination for mawb');
    }

    const date = new Date();
    const [allConversionRates, taxRates] = await Promise.all([
      CurrencyConversionService.getAllExchRates(date),
      axios.get(`${process.env.FINANCE_URL}/tax/all-deminimis`),
    ]);

    const arrayOfArray = await Promise.all(allGLsInMawb.map((gl) => this.getParcelsByGlId(gl.id)));
    const arrayParcels = arrayOfArray.reduce((accumulator, current) => accumulator.concat(current));
    const aboveDeminimisItems = [];
    const allParcels = [];
    const belowDeminimisItems = [];
    const merchantMap = new Map();

    for (const merchant of allMerchant) {
      merchantMap.set(AesUtils.CrtCounterDecrypt(merchant.merchant_name), merchant);
    }

    for (const parcelNotDecrypt of arrayParcels) {
      const parcel = AesUtils.decryptParcel(parcelNotDecrypt);
      parcel.merchant = merchantMap.get(parcel.merchant_name);
      allParcels.push(parcel);

      const thresholdList = taxRates.data.filter(
        (taxItem: any) =>
          new Date(taxItem.valid_from) <= date &&
          taxItem.country.toLowerCase() ===
            CountryService.getCountryFullName(destination.country).toLowerCase(),
      );

      if (destination.threshold_check && thresholdList.length > 0) {
        const thresholdObj = sortBy(thresholdList, (o) => o.valid_from)[0];
        const rate = CurrencyConversionService.convertCurrency(
          allConversionRates,
          parcel.merchant_declared_currency.toUpperCase(),
          thresholdObj.currency.toUpperCase(),
        );

        // if cannot get any rate and the destination country not using same currency with origin country, then reject
        if (
          !rate &&
          parcel.merchant_declared_currency.toLowerCase() !== destination.currency.toLowerCase()
        ) {
          throw new Error(
            `Cannot get rate of current period with Currency: ${destination.currency}`,
          );
        }

        const separate = this.separateItem(rate, parcel, destination, thresholdObj);

        if (separate.isBelow) {
          belowDeminimisItems.push(parcel.id);
        } else {
          aboveDeminimisItems.push(parcel.id);
        }

        if (
          !Object.prototype.hasOwnProperty.call(
            parcelNotDecrypt,
            'converted_merchant_declared_value',
          )
        ) {
          parcel.converted_merchant_declared_value = separate.converted_merchant_declared_value;
          await this.updateMerchantDeclaredValue(parcel);
        }
      } else {
        const rate = CurrencyConversionService.convertCurrency(
          allConversionRates,
          parcel.merchant_declared_currency.toUpperCase(),
          destination.currency.toUpperCase(),
        );

        // if cannot get any rate and the destination country not using same currency with origin country, then reject
        if (
          !rate &&
          parcel.merchant_declared_currency.toLowerCase() !== destination.currency.toLowerCase()
        ) {
          throw new Error(
            `Cannot get rate of current period with Currency: ${destination.currency}`,
          );
        }

        const separate = this.separateItem(rate, parcel, destination);

        if (
          !Object.prototype.hasOwnProperty.call(
            parcelNotDecrypt,
            'converted_merchant_declared_value',
          )
        ) {
          parcel.converted_merchant_declared_value = separate.converted_merchant_declared_value;
          await this.updateMerchantDeclaredValue(parcel);
        }

        belowDeminimisItems.push(parcel.id);
      }
    }

    return {
      parcelsAbove: aboveDeminimisItems,
      parcelsBelow: belowDeminimisItems,
      allParcels,
      destination,
      allConversionRates,
    };
  },

  /**
   *
   * @param {*} rate rate object
   * @param {*} parcel parcel object
   * @param {*} destination destination object
   * @param {*} thresholdObj threshold tax
   * @returns type of parcel (below/above) and converted merchant declared value
   */
  separateItem(rate: any, parcel: any, destination: any, thresholdObj?: any) {
    // if cannot get any rate, then destination and origin is using same currency, rate should equal 1
    if (parcel.de_minimis_tier) {
      return {
        converted_merchant_declared_value: parcel.converted_merchant_declared_value,
        isBelow: parcel.de_minimis_tier === ENUM.DE_MINIMIS_TIER.B,
      };
    }

    rate = rate || { destination_currency: 1 };
    let converted_merchant_declared_value = 0;
    const total_merchant_declared_value =
      Object.prototype.hasOwnProperty.call(parcel, 'item') && parcel.item.length > 0
        ? parcel.item.reduce(
            (acc: number, item: any) => Number.parseFloat(item.total_declared_value) + acc,
            0,
          )
        : 0;

    if (parcel.lmd.toLowerCase().includes('kerry')) {
      for (const it of parcel.item) {
        it.converted_total_declared_value = (
          it.total_declared_value * Number.parseFloat(rate.destination_currency)
        ).toFixed(2);
      }

      const fob = (
        total_merchant_declared_value * Number.parseFloat(rate.destination_currency)
      ).toFixed(2);

      if (parcel.lmd.toLowerCase() === 'kerry hong kong') {
        return {
          converted_merchant_declared_value: fob,
          isBelow: false,
        };
      }

      const insurance = Number.parseFloat(fob) * (1 / 100);
      const weight = UnitConverterUtils.getChargeableWeight(parcel, 'gross weight', 'kg');
      const freight = 130 * weight;
      const cif = Number.parseFloat(fob) + insurance + freight;
      converted_merchant_declared_value = Number(cif.toFixed(2));

      if (
        destination.threshold_check &&
        thresholdObj &&
        Number.parseFloat(fob) <= thresholdObj.threshold &&
        cif <= KERRY_CIF_THRESHOLD
      ) {
        return {
          converted_merchant_declared_value,
          isBelow: true,
        };
      }

      return {
        converted_merchant_declared_value,
        isBelow: false,
      };
    }

    converted_merchant_declared_value =
      total_merchant_declared_value * Number.parseFloat(rate.destination_currency);

    if (
      thresholdObj &&
      converted_merchant_declared_value >= thresholdObj.threshold &&
      destination.threshold_check
    ) {
      return {
        converted_merchant_declared_value: converted_merchant_declared_value.toFixed(2),
        isBelow: false,
      };
    }

    return {
      converted_merchant_declared_value: converted_merchant_declared_value.toFixed(2),
      isBelow: true,
    };
  },

  /**
   * update converted_merchant_declared_value for parcel item, if error occurred, just log the exception
   * @param {*} parcel parcel to be update
   */
  async updateMerchantDeclaredValue(parcel: any) {
    try {
      await Daos.manifest_items.updateItemResolveConflict({
        id: parcel.id,
        converted_merchant_declared_value: parcel.converted_merchant_declared_value,
      });
      logger.info({
        message: 'parcelClassification Updated Successfully-converted_merchant_declared_value',
        parcelId: parcel.id,
        parcelConvertedMerchantDeclaredValue: parcel.converted_merchant_declared_value,
      });
    } catch (error) {
      logger.error({ parcelId: parcel.id, error });

      throw new Error(`parcelClassification Cannot Update Converted Value ${parcel.id}`);
    }
  },

  /**
   * Generate and return buffer of PDF Proforma for export/import process
   * @param {*} parcels array of parcels
   * @param {*} merchants array of merchants
   * @param {*} opHubs array of operation hubs
   * @returns Object contain status and buffer data
   */
  async generatePdfProformaInvoice(parcels: any[], merchants: any[], opHubs: any[]) {
    if (
      !Array.isArray(parcels) ||
      parcels.length === 0 ||
      !Array.isArray(merchants) ||
      merchants.length === 0 ||
      !Array.isArray(opHubs) ||
      opHubs.length === 0
    ) {
      logger.info({
        message: 'blank input',
        'parcels length': parcels.length,
        'merchants length': merchants.length,
        'opHubs length': opHubs.length,
      });

      return {
        message: 'blank input',
        buffer: '',
        success: false,
      };
    }

    try {
      logger.info({ message: 'generatePdfProformaInvoice start' });
      const parcelPayloads = parcels.map((parcel) => {
        const merchant = merchants.find(
          (merItem: { merchant_account_number: any }) =>
            merItem.merchant_account_number === parcel.merchant_account_number,
        );
        const opHub = opHubs.find(
          (opHubItem: { operation_hub: any }) => opHubItem.operation_hub === parcel.operation_hub,
        );

        return {
          merchant_name: AesUtils.CrtCounterDecrypt(merchant.merchant_name || ''),
          merchant_street: AesUtils.CrtCounterDecrypt(merchant.street || ''),
          merchant_post_code: merchant.postal_code || '',
          merchant_state: AesUtils.CrtCounterDecrypt(merchant.state || ''),
          merchant_city: AesUtils.CrtCounterDecrypt(merchant.city || ''),
          merchant_country: AesUtils.CrtCounterDecrypt(merchant.country || ''),
          op_hub_country: opHub.country || '',
          merchant_declared_currency: parcel.merchant_declared_currency || '',
          item: parcel.item,
          shipping_and_insurance_cost: parcel.shipping_and_insurance_cost || 0,
          tracking_id: parcel.tracking_id || '',
          recipient_first_name: parcel.recipient_first_name || '',
          recipient_last_name: parcel.recipient_last_name || '',
          recipient_addressline1: parcel.recipient_addressline1 || '',
          recipient_addressline2: parcel.recipient_addressline2 || '',
          recipient_addressline3: parcel.recipient_addressline3 || '',
          city_suburb: parcel.city_suburb || '',
          state: parcel.state || '',
          postcode: parcel.postcode || '',
          country: parcel.country || '',
          length:
            parcel.dimensions_unit === 'cm'
              ? Number(parcel.length).toFixed(2)
              : UnitConverterUtils.intocm(Number(parcel.length)).toFixed(2),
          width:
            parcel.dimensions_unit === 'cm'
              ? Number(parcel.width).toFixed(2)
              : UnitConverterUtils.intocm(Number(parcel.width)).toFixed(2),
          height:
            parcel.dimensions_unit === 'cm'
              ? Number(parcel.height).toFixed(2)
              : UnitConverterUtils.intocm(Number(parcel.height)).toFixed(2),
          weight:
            parcel.weight_unit === 'kg'
              ? Number(parcel.weight).toFixed(2)
              : UnitConverterUtils.lbtokg(Number(parcel.weight)).toFixed(2),
        };
      });

      const res = await axios.post(
        `${process.env.PDF_FUNC_URL}/proforma-invoice`,
        {
          parcels: parcelPayloads,
        },
        { responseType: 'arraybuffer' },
      );

      return {
        type: 'Buffer convert to JSON',
        buffer: JSON.stringify(res?.data),
        success: true,
      };
    } catch (error) {
      logger.error({ error });

      throw error;
    }
  },

  /**
   * this function generate custom manifest and mawb pdf
   * @param {*} mawb mawb object
   * @param {*} gaylords array of gaylords
   * @param {*} classData classification parcel data
   * @param {*} allOpHubs array of operation hubs
   * @param {Boolean} sendSftp will send sftp
   * @returns archive object and other data
   */
  async generateCustomManifest(
    mawb: any,
    gaylords: any[],
    classData: any,
    allOpHubs: any[],
    sendSftp: boolean,
    allConversionRates: any[],
    customBrokerName: string,
    customConfig = {},
  ) {
    const aboveDeminisIds = classData.parcelsAbove;
    const { allParcels } = classData;
    const { destination } = classData;
    const countryCode = destination.country;

    let mawb_buffer: Buffer = null;
    let flight_no = '';
    let genMawbSuccess = false;
    let local_std = '';
    let local_sta = '';
    let chargeableWeight = '';
    const lmd = destination.lmds.find((item) => item.id === gaylords[0].lmd_provider_name);
    const { tracking_status, point_of_discharge, id } = mawb;
    let final_sta_std_message = {} as any;

    if (!!tracking_status && tracking_status.length > 0) {
      const fwb_message = tracking_status.filter((x) => {
        return x.type === 'FWB';
      });

      const std_sta_message = tracking_status.filter(
        (x) => x.type === 'STASTD' || x.status === 'STASTD',
      );

      if (!!std_sta_message && std_sta_message.length > 0) {
        const fi = /\n(\d{3}-\d{8})([A-Z]{3})([A-Z]{3})\/.*/m.exec(fwb_message.at(-1).raw_message);
        final_sta_std_message = std_sta_message.find(
          (ts) => ts.destination && ts.destination === fi[3],
        );
        local_std = final_sta_std_message.local_STD;
        local_sta = final_sta_std_message.local_STA;
      }

      const finishStatus = tracking_status.find(
        (x) => x.latest_tracking_status === ENUM.mawbStatus.fin,
      );
      const flightInfo = finishStatus && (finishStatus.second_sector || finishStatus.first_sector);

      if (!local_sta && flightInfo && flightInfo.flight_date) {
        local_sta = flightInfo.flight_date.slice(0, -5);
      }

      for (const item of allParcels) {
        const op_hub = allOpHubs.find((x) => {
          return x.operation_hub === item.operation_hub;
        });
        item.op_hub = op_hub;
        item.mawb = mawb;
        item.mawb.mawb_no = id;
      }

      if (!!fwb_message && fwb_message.length > 0) {
        const parsing_result = await genFwbPdf(fwb_message.at(-1).raw_message);
        mawb_buffer = parsing_result.buffer;
        flight_no = parsing_result.flight_no;
        chargeableWeight = parsing_result.chargeableWeight;
        genMawbSuccess = true;

        for (const item of allParcels) {
          item.mawb.flight_no = flight_no;
        }

        // trigger send SFTP to AusPost
        if (allParcels[0].lmd === ENUM.lmdNames.AusPost) {
          const fixed_data = {
            flight_no,
            mawb_no: id,
            local_sta,
            point_of_discharge,
          };

          if (sendSftp) {
            AusPostUtils.sendSftpForAU(allParcels, fixed_data);
          }
        } else {
          allParcels.forEach((item: any) => {
            const final_FWB_message =
              Array.isArray(fwb_message) && fwb_message.length > 0 ? fwb_message.at(-1) : {};
            item.mawb.STA = local_sta;
            item.mawb.destination = final_sta_std_message.destination;
            item.mawb.origin = final_sta_std_message.origin;
            item.mawb.mawb_date = final_FWB_message.date;
          });
        }
      }
    }

    const belowItems = [];
    const aboveItems = [];

    for (const p of allParcels) {
      if (aboveDeminisIds.includes(p.id)) {
        aboveItems.push(p);
      } else {
        belowItems.push(p);
      }
    }

    const archive = await this.createCustomManifestCSV(
      {
        countryCode,
        destinationCurrency: destination.currency,
        lmd,
        customBrokerName,
      },
      {
        mawb,
        flight_no,
        local_sta,
        local_std,
      },
      allConversionRates,
      allOpHubs,
      aboveItems,
      belowItems,
      allParcels,
      gaylords,
      customConfig,
    );

    return {
      archive,
      lmd,
      local_sta,
      local_std,
      flight_no,
      mawbPdfData: {
        mawb_buffer,
        chargeableWeight,
        success: genMawbSuccess,
      },
    };
  },

  /**
   * This function generate custom manifest documents
   * @param {*} mawb mawb data
   * @param {*} gaylords gaylords data
   * @param {*} classData parcel in classification
   * @param {*} allOpHubs all operation hubs data
   * @param {*} allConversionRates Currency conversion rates
   * @param {*} customBrokerName custom broker name
   * @param {*} customBroker custom broker item
   * @returns archive file that contain the custom manifest
   */
  async generateCustomManifestOnly(
    mawb,
    gaylords,
    classData,
    allOpHubs,
    allConversionRates,
    customBroker,
    flightInfo,
  ) {
    try {
      const aboveDeminisIds = classData.parcelsAbove;
      const belowDeminisIds = classData.parcelsBelow;
      const { allParcels } = classData;
      const { destination } = classData;
      const countryCode = destination.country;

      const lmd = destination.lmds.find((item) => item.id === gaylords[0].lmd_provider_name);

      for (const item of allParcels) {
        const op_hub = allOpHubs.find((x) => {
          return x.operation_hub === item.operation_hub;
        });
        item.op_hub = op_hub;
        item.mawb = mawb;
        item.mawb.mawb_no = mawb._partitionKey;
        item.mawb.flight_no = flightInfo.flight_no;
        item.mawb.STA = flightInfo.local_sta;
        item.mawb.mawb_date = flightInfo.local_std;
      }

      let belowItems = [];

      if (Array.isArray(belowDeminisIds) && belowDeminisIds.length > 0) {
        belowItems = allParcels.filter((x) => {
          return belowDeminisIds.includes(x.id);
        });
      }

      let aboveItems = [];

      if (Array.isArray(aboveDeminisIds) && aboveDeminisIds.length > 0) {
        aboveItems = allParcels.filter((x) => {
          return aboveDeminisIds.includes(x.id);
        });
      }

      return await this.createCustomManifestCSV(
        {
          countryCode,
          destinationCurrency: destination.currency,
          lmd,
          customBrokerName: customBroker.name,
        },
        {
          mawb,
          ...flightInfo,
        },
        allConversionRates,
        allOpHubs,
        aboveItems,
        belowItems,
        allParcels,
        gaylords,
      );
    } catch (error) {
      logger.error({ error });

      return [];
    }
  },

  async createCustomManifestCSV(
    destinationInfo,
    flightInfo,
    allConversionRates,
    allOpHubs,
    aboveItems,
    belowItems,
    allParcels,
    containers,
    customConfig = {} as any,
  ) {
    const { countryCode, destinationCurrency, lmd, customBrokerName } = destinationInfo;
    const { mawb, flight_no, local_sta, local_std } = flightInfo;
    const archive = [];

    switch (countryCode) {
      case 'NZ': {
        if (allParcels.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.custom_broker_manifest_NZ_template(
              allParcels,
              allConversionRates,
              destinationCurrency,
            ),
          );
          archive.push({
            name: `${mawb.id}_manifest${XLSX_EXT}`,
            buffer,
          });
        }

        break;
      }

      case 'AU': {
        if (allParcels[0].lmd === ENUM.lmdNames.CouriersPlease) {
          const CBManifestBuffer = ReportUtils.export_custom_broker_manifest_CPL_AU_template(
            allParcels,
            allParcels[0].op_hub,
          );
          archive.push({ name: 'Custom manifest.xlsx', buffer: CBManifestBuffer });
          break;
        }

        // case when LMD is Au Post
        if (aboveItems.length > 0) {
          const aboveDeminisBuffer = AupostXlsService.generateManifestXlsBuffer(
            aboveItems,
            local_sta,
          );
          archive.push({ name: 'High value item manifest.xls', buffer: aboveDeminisBuffer });
        }

        if (belowItems.length > 0) {
          const belowDeminisBuffer = AupostXlsService.generateManifestXlsBuffer(
            belowItems,
            local_sta,
          );
          archive.push({ name: 'Low value item manifest.xls', buffer: belowDeminisBuffer });
        }

        break;
      }

      case 'TH': {
        if (lmd.lmd_provider_name.toLowerCase().includes('kerry')) {
          if (aboveItems.length > 0) {
            const aboveDeminisBuffer = Buffer.from(
              ReportUtils.custom_broker_manifest_kerry_TH(aboveItems),
            );
            archive.push({
              name: HIGH_VALUE_MANIFEST_FILENAME + XLSX_EXT,
              buffer: aboveDeminisBuffer,
            });
          }

          if (belowItems.length > 0) {
            const belowDeminisBuffer = Buffer.from(
              ReportUtils.custom_broker_manifest_kerry_TH(belowItems),
            );
            archive.push({
              name: LOW_VALUE_MANIFEST_FILENAME + XLSX_EXT,
              buffer: belowDeminisBuffer,
            });
          }
        }

        break;
      }

      case 'KR': {
        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_KR_Template(aboveItems, destinationCurrency),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_KR_Template(belowItems, destinationCurrency),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'JP': {
        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_JP_Template(
              aboveItems,
              allConversionRates,
              'JPY',
            ),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_JP_Template(
              belowItems,
              allConversionRates,
              'JPY',
            ),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'HK': {
        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_HK_Template(aboveItems),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_HK_Template(belowItems),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'GB': {
        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_GB_Template(aboveItems),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_GB_Template(belowItems),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'TW': {
        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_TW_Template(aboveItems),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_TW_Template(belowItems),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'ID': {
        const opHub = allOpHubs.find((x) => {
          return x.operation_hub === allParcels[0].operation_hub;
        });

        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_ID_template(
              aboveItems,
              allConversionRates,
              destinationCurrency,
              opHub,
              customBrokerName,
            ),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            ReportUtils.export_custom_broker_manifest_ID_template(
              belowItems,
              allConversionRates,
              destinationCurrency,
              opHub,
              customBrokerName,
            ),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'SG': {
        if (aboveItems.length > 0) {
          const data = await ReportUtils.export_custom_broker_manifest_SG_Template(aboveItems);
          const aboveDeminisBuffer = Buffer.from(data);
          archive.push({
            name: HIGH_VALUE_MANIFEST_FILENAME + XLSX_EXT,
            buffer: aboveDeminisBuffer,
          });
        }

        if (belowItems.length > 0) {
          const data = await ReportUtils.export_custom_broker_manifest_SG_Template(belowItems);
          const belowDeminisBuffer = Buffer.from(data);
          archive.push({
            name: LOW_VALUE_MANIFEST_FILENAME + XLSX_EXT,
            buffer: belowDeminisBuffer,
          });
        }

        break;
      }

      case 'PH': {
        if (aboveItems.length > 0) {
          const buffer = Buffer.from(
            await ReportUtils.export_custom_broker_manifest_PH_Template(
              flight_no,
              local_sta,
              local_std,
              aboveItems,
              mawb,
            ),
          );
          archive.push({ name: `High Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        if (belowItems.length > 0) {
          const buffer = Buffer.from(
            await ReportUtils.export_custom_broker_manifest_PH_Template(
              flight_no,
              local_sta,
              local_std,
              belowItems,
              mawb,
            ),
          );
          archive.push({ name: `Low Value Manifest ${mawb.mawb_no}.xlsx`, buffer });
        }

        break;
      }

      case 'MY': {
        if (aboveItems.length > 0) {
          const customManifestBuffer = Buffer.from(
            await ReportUtils.export_custom_broker_manifest_MY_Template(
              flight_no,
              local_sta,
              local_std,
              aboveItems,
              destinationCurrency,
              mawb,
              allConversionRates,
            ),
          );

          if (customConfig) {
            const lmdZoneName = customConfig.goToEastMY ? 'EAST' : 'WEST';
            archive.push({
              name: `High Value Manifest ${mawb.mawb_no}_${lmdZoneName}.xlsx`,
              buffer: customManifestBuffer,
            });
          } else {
            archive.push({
              name: `High Value Manifest ${mawb.mawb_no}.xlsx`,
              buffer: customManifestBuffer,
            });
          }
        }

        if (belowItems.length > 0) {
          const customManifestBuffer = Buffer.from(
            await ReportUtils.export_custom_broker_manifest_MY_Template(
              flight_no,
              local_sta,
              local_std,
              belowItems,
              destinationCurrency,
              mawb,
              allConversionRates,
            ),
          );

          if (customConfig) {
            const lmdZoneName = customConfig.goToEastMY ? 'EAST' : 'WEST';
            archive.push({
              name: `Low Value Manifest ${mawb.mawb_no}_${lmdZoneName}.xlsx`,
              buffer: customManifestBuffer,
            });
          } else {
            archive.push({
              name: `Low Value Manifest ${mawb.mawb_no}.xlsx`,
              buffer: customManifestBuffer,
            });
          }
        }

        break;
      }

      default: {
        if (lmd.lmd_provider_name === ENUM.lmdNames.LMG) {
          const buffer = Buffer.from(
            await ReportUtils.exportCustomBrokerManifestLMGUSTemplate(
              allParcels,
              mawb.tracking_status.find((m) => m.tranx_type === EnumMawbTranxType.CREATED),
              containers,
              local_sta,
            ),
          );
          archive.push({
            name: `${mawb.mawb_no}_manifest${XLSX_EXT}`,
            buffer,
          });
        } else {
          if (aboveItems.length > 0) {
            const aboveDeminisBuffer = Buffer.from(
              ReportUtils.export_custom_broker_manifest_SEA_Template(
                aboveItems,
                destinationCurrency,
              ),
            );
            archive.push({
              name: HIGH_VALUE_MANIFEST_FILENAME + XLSX_EXT,
              buffer: aboveDeminisBuffer,
            });
          }

          if (belowItems.length > 0) {
            const belowDeminisBuffer = Buffer.from(
              ReportUtils.export_custom_broker_manifest_SEA_Template(
                belowItems,
                destinationCurrency,
              ),
            );
            archive.push({
              name: LOW_VALUE_MANIFEST_FILENAME + XLSX_EXT,
              buffer: belowDeminisBuffer,
            });
          }
        }
      }
    }

    return archive;
  },

  /**
   * Find parcel base on parcel number or Tracking ID
   * @param {*} searchValue
   * @returns [] parcels
   */
  async getParcelsByParcelNumberOrTrackingId(searchValue: string) {
    try {
      const querySpec = {
        query: `
          SELECT TOP 1 *
          FROM c 
          WHERE 
          ARRAY_CONTAINS([c.id, c.tracking_id], @searchValue) OR
          EXISTS (
              SELECT VALUE n
              FROM n IN c.tracking_id_history
              WHERE n.tracking_id=@searchValue
          )
        ORDER BY c.order_date DESC`,
        parameters: [
          {
            name: '@searchValue',
            value: searchValue,
          },
        ],
      };

      return await Daos.manifest_items.find(querySpec);
    } catch (error: any) {
      logger.error({ error });

      return [];
    }
  },

  groupDateBaseOnStatus(statusDates) {
    if (!statusDates?.length) {
      return new Error('manifest-item.service Error: invalid statusGroups');
    }

    const groupedByStatus = groupBy(statusDates, 'status');

    return Object.entries(groupedByStatus).map(([status, statusChild]) => {
      const orderedStatusChild = sortBy(statusChild, ['date', 'event_datetime', 'timestamp']);
      const date = orderedStatusChild.map(
        (each) => each.timestamp || each.event_datetime || each.date,
      );

      return { status, date };
    });
  },

  groupDateStatusPLSSearch(statusDates) {
    const groupedByStatus = groupBy(statusDates, 'status');

    return Object.entries(groupedByStatus).map(([status, statusChild]) => {
      const orderedStatusChild = sortBy(statusChild, ['date', 'event_datetime', 'timestamp']);
      const statusGrouped = orderedStatusChild.map((each) => ({
        date: each.timestamp || each.event_datetime || each.date,
        receiveStatusDate: each.date || each.timestamp || each.event_datetime,
        description: each.description,
        statusLocation: each.statusLocation,
      }));

      return { status, statusGrouped };
    });
  },

  async processGaylordAndMawbInfo(parcel: any) {
    // Create a new object instead of modifying the original
    const result = { ...parcel };
    const gaylordNo = result.gaylord_no || '';
    result.mawb_no = '';
    result.overpack_id = '';
    result.tracking_status_merged = this.groupDateStatusPLSSearch(result.tracking_status);

    if (gaylordNo) {
      const gaylord = await GaylordServices.getGaylordById(gaylordNo);
      result.overpack_id = gaylord?.overpack_id;
      const mawbNo = gaylord?.mawb_no;

      if (mawbNo) {
        result.mawb_no = mawbNo;
        const mawbs = await MawbService.getMawb(mawbNo);

        if (!isEmpty(mawbs)) {
          result.tracking_status_merged = PlsReportUtils.combineStatusesForPLSSearch(
            [...result.tracking_status, ...gaylord.tracking_status],
            mawbs,
          );

          const mergeTrackingStt: any[] = Object.values(
            result.tracking_status_merged.reduce(
              (
                sttGroup: any,
                {
                  status,
                  date,
                  receiveStatusDate,
                  description,
                  statusLocation,
                }: {
                  status: string;
                  receiveStatusDate: string;
                  date: string;
                  description: string;
                  statusLocation: string;
                },
              ) => {
                sttGroup[status] = sttGroup[status] || {
                  status,
                  sequence: StatusMappingService.getStatus(status)?.sequence ?? 0,
                  statusGrouped: [],
                };
                sttGroup[status].statusGrouped.push({
                  date,
                  receiveStatusDate,
                  description,
                  statusLocation,
                });

                return sttGroup;
              },
              {},
            ),
          );

          mergeTrackingStt.sort((a, b) => {
            if (a.sequence !== b.sequence) {
              return a.sequence - b.sequence;
            }

            // If sequences are equal, use the earliest date from each group
            return (
              new Date(a.statusGrouped[0]?.date || 0).getTime() -
              new Date(b.statusGrouped[0]?.date || 0).getTime()
            );
          });

          result.tracking_status_merged = mergeTrackingStt;
          result.latest_tracking_status = mergeTrackingStt.at(-1).status;
        }
      }
    }

    return result;
  },

  async processB2BHawbInfo(parcel: any) {
    const result = { ...parcel };

    // Check if this is a B2B shipment
    if (parcel.service_option === 'b2b' && parcel.hawb_id) {
      try {
        // Retrieve HAWB data using existing HawbService
        const hawb = await HawbService.getHawbById(parcel.hawb_id);

        if (hawb) {
          // Override mawb_no with HAWB data (higher priority than gaylord)
          result.mawb_no = hawb.mawb_no;
          result.hawb_id = hawb.id;
          result.hawb_no = hawb.hawb_no;

          // Add PD invoicing information from HAWB
          result.pd_invoicing_statuses = hawb.pd_invoicing_status || [];
          result.latest_pd_invoicing_status = this.getLatestPDInvoicingStatus(
            hawb.pd_invoicing_status,
          );
          result.pd_amount = hawb.pd_amount;
          result.pd_currency = hawb.pd_currency;
          result.pd_invoice_no = hawb.pd_invoice_no;

          // Note: DT_invoicing_status is already available from manifest item level
          // No additional processing needed for DT statuses
        }
      } catch (error) {
        logger.warn({
          message: 'Failed to retrieve HAWB data for B2B shipment',
          hawbId: parcel.hawb_id,
          error,
        });
      }
    }

    return result;
  },

  getLatestPDInvoicingStatus(pdInvoicingStatuses: IPDInvoicingStatus[]): string | undefined {
    if (!pdInvoicingStatuses || pdInvoicingStatuses.length === 0) {
      return undefined;
    }

    // Sort by date and return the latest status
    const sortedStatuses = pdInvoicingStatuses.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    );

    return sortedStatuses[0]?.status;
  },

  processTrackingStatuses(parcel: ParcelWithGroup) {
    // Create a new object instead of modifying the original
    const result = { ...parcel };
    const parcelExceptionStatus = [];
    const exceptionStatusesForPLSSearch = new Set(StatusService.getExceptionStatusForPLSSearch());
    const warehouseStatusesForPLSSearch = StatusService.getWarehouseStatusForPLSSearch();
    const flightStatusesForPLSSearch = StatusService.getFlightStatusForPLSSearch();
    const mapManifestStatusAndSequence = {};
    result.lastMileStatus = [];

    for (const status of Object.values(StatusMappingService.parcelStatusMapping)) {
      mapManifestStatusAndSequence[status.manifestStt] = status.sequence;
    }

    for (const trackingStatus of result.tracking_status_merged) {
      const statusSequence = mapManifestStatusAndSequence[trackingStatus.status];

      if (exceptionStatusesForPLSSearch.has(trackingStatus.status)) {
        parcelExceptionStatus.push(trackingStatus);
      } else if (warehouseStatusesForPLSSearch[trackingStatus.status]) {
        warehouseStatusesForPLSSearch[trackingStatus.status] = trackingStatus.statusGrouped;
      } else if (flightStatusesForPLSSearch[trackingStatus.status]) {
        flightStatusesForPLSSearch[trackingStatus.status] = trackingStatus.statusGrouped;
      } else if (statusSequence >= 3 && statusSequence <= 7) {
        result.lastMileStatus.push(trackingStatus);
      }
    }

    result.warehouseStatus = Object.keys(warehouseStatusesForPLSSearch).map((warehouseStatus) => ({
      status: warehouseStatus,
      statusGrouped: warehouseStatusesForPLSSearch[warehouseStatus],
    }));

    result.flightStatus = Object.keys(flightStatusesForPLSSearch).map((flightStatus) => ({
      status: flightStatus,
      statusGrouped: flightStatusesForPLSSearch[flightStatus],
    }));

    const statusesNeedLastTimestamp = StatusService.getStatusNeedLastTimetamp();
    parcelExceptionStatus.sort(
      (a, b) =>
        new Date(
          statusesNeedLastTimestamp.includes(b.status)
            ? b.statusGrouped.at(-1).date
            : b.statusGrouped.at(0).date,
        ).getTime() -
        new Date(
          statusesNeedLastTimestamp.includes(a.status)
            ? a.statusGrouped.at(-1).date
            : a.statusGrouped.at(0).date,
        ).getTime(),
    );

    result.exception_status = parcelExceptionStatus.map(({ status }) => status);

    return result;
  },

  async getParcelInfoForReportPage(searchValue: string): Promise<any> {
    try {
      const parcels = await this.getParcelsByParcelNumberOrTrackingId(searchValue);

      if (isEmpty(parcels)) {
        return;
      }

      // Start with a copy of the first parcel
      let processedParcel = { ...parcels[0] };

      // Process the parcel through each stage, keeping the chain of transformations
      processedParcel = await this.processGaylordAndMawbInfo(processedParcel);

      // Process B2B HAWB info (will override mawb_no if B2B)
      processedParcel = await this.processB2BHawbInfo(processedParcel);

      processedParcel = this.processTrackingStatuses(processedParcel);

      // get all fellow children parcels
      if (processedParcel.parent_id) {
        const childrenParcels: { id: string }[] = await this.getAllFellowChildrenParcel(
          processedParcel.parent_id,
          ['id'],
        );

        if (childrenParcels.length > 0) {
          processedParcel = {
            ...processedParcel,
            children_parcels: childrenParcels
              .filter(({ id }) => id !== processedParcel.id)
              .map(({ id }) => id),
          };
        }
      }

      const decryptedParcel = AesUtils.decryptParcel(processedParcel);

      const updateHistory = await parcelHistoryService.getChangeOfBookingHistory(
        decryptedParcel.id,
      );
      decryptedParcel.updateHistory =
        parcelHistoryService.getChangeOfBookingUpdateHistoryInSequence(
          decryptedParcel,
          updateHistory,
        );

      ShipmentUtils.setAttemptedDeliveryToParcel(decryptedParcel, StatusMappingService);

      return decryptedParcel;
    } catch (error: any) {
      logger.error({ searchValue, error });

      return undefined;
    }
  },

  /**
   * Get booking capacity utilisation in current week
   */
  async getLmdReceivedBookings(
    orgCountry: string,
    startDate: any,
    endDate: any,
    destinationGroups: any[],
  ) {
    const query = {
      query: `
        SELECT c.destination_group
        FROM c
        WHERE (IS_DEFINED(c.tracking_id) OR c.latest_tracking_status = "Booked")
        AND IS_DEFINED(c.destination_group)
        AND IS_DEFINED(c.origin_country) 
        AND c.origin_country = @orgCountry
        AND c.tracking_status[0].date >= @startDate
        AND c.tracking_status[0].date <= @endDate
        AND ARRAY_CONTAINS(@destinationGroups, c.destination_group)
        `,
      parameters: [
        {
          name: '@orgCountry',
          value: orgCountry,
        },
        {
          name: '@startDate',
          value: startDate,
        },
        {
          name: '@endDate',
          value: endDate,
        },
        {
          name: '@destinationGroups',
          value: destinationGroups,
        },
      ],
    };
    const parcels = await Daos.manifest_items.find(query);

    return countBy(parcels, 'destination_group');
  },

  /**
   * Get manifest items by a list of tracking_id.
   *
   * List of tracking_id will be seperated and query by batch
   * @param queryBothTIDAndID if true then query by both tracking_id and id
   */
  async getManifestItemsByTrackingIDs(
    trackingIDs: string[],
    fields: string[] | null = null,
    queryBothTIDAndID: boolean = false,
  ) {
    if (trackingIDs.length === 0) return [];
    let queryFields = '*';
    let queryIDCondition = '';

    if (Array.isArray(fields) && fields.length > 0) {
      fields = fields.map((x) => `c.${x}`);
      queryFields = fields.join(', ');
    }

    if (queryBothTIDAndID) {
      queryIDCondition = 'OR ARRAY_CONTAINS(@trackingIds, c.id)';
    }

    const parcels = [];

    for (const batchedTrackingIds of CommonUtils.chunkArrayGenerator(trackingIDs, 500)) {
      try {
        const querySpec = {
          query: `SELECT ${queryFields} FROM c WHERE ARRAY_CONTAINS(@trackingIds, c.tracking_id) ${queryIDCondition}`,
          parameters: [
            {
              name: '@trackingIds',
              value: batchedTrackingIds,
            },
          ],
        };
        const foundParcels = await Daos.manifest_items.find(querySpec);

        // Add idSearchString property to each item if queryBothTIDAndID flag is true
        if (queryBothTIDAndID) {
          for (const parcel of foundParcels) {
            const foundIndex = batchedTrackingIds.findIndex(
              (id) => id === parcel.tracking_id || id === parcel.id,
            );
            parcel.idSearchString = foundIndex === -1 ? null : batchedTrackingIds[foundIndex];
          }
        }

        parcels.push(...foundParcels);
      } catch (error: any) {
        logger.error({ message: error.message, trackingIDs });
      }
    }

    return queryBothTIDAndID
      ? [...new Map(parcels.map((parcel) => [parcel.idSearchString, parcel])).values()]
      : [...new Map(parcels.map((parcel) => [parcel.tracking_id, parcel])).values()];
  },

  async getManifestItemsByIDs(parcelIds: string[], fields?: string[]): Promise<any[]> {
    if (parcelIds.length === 0) return [];
    let queryFields = '*';

    if (Array.isArray(fields) && fields.length > 0) {
      fields = fields.map((x) => `c.${x}`);
      queryFields = fields.join(', ');
    }

    try {
      const querySpec = {
        query: `SELECT ${queryFields} FROM c WHERE ARRAY_CONTAINS(@parcelIds, c.id)`,
        parameters: [
          {
            name: '@parcelIds',
            value: parcelIds,
          },
        ],
      };

      return await Daos.manifest_items.find(querySpec);
    } catch (error: any) {
      logger.error({ message: error.message, parcelIds });

      return [];
    }
  },

  getManifestItemsFromMultipleGaylords(gaylordIds: string[], fields?: string[]) {
    let queryFields = '*';

    if (Array.isArray(fields) && fields.length > 0) {
      fields = fields.map((x) => `c.${x}`);
      queryFields = fields.join(', ');
    }

    const querySpec = {
      query: `SELECT ${queryFields}
      FROM c WHERE ARRAY_CONTAINS(@gaylordIds, c.gaylord_no)`,
      parameters: [
        {
          name: '@gaylordIds',
          value: gaylordIds,
        },
      ],
    };

    return Daos.manifest_items.find(querySpec);
  },

  isProcessRejectedBooking(parcel: ManifestItem) {
    return (
      parcel.tracking_id === parcel.id &&
      parcel.tracking_status.every(
        (tracking: any) =>
          tracking.status !==
          StatusMappingService.getManifestStatus(ENUM.parcelStatus.lmd_receive_booking),
      )
    );
  },

  async generateAndSendmailCBpart2MY(
    mawb: any,
    gaylords: any[],
    parcelClassificationData: any,
    allOpHubs: any[],
    allConversionRates: any[],
    customBrokerName: string,
    allMerchants: any[],
    zoneName: string,
    customBroker: any,
    req: any,
    parcels: any[],
  ) {
    const parcelClassificationCopy = { ...parcelClassificationData };
    parcelClassificationCopy.allParcels = parcels;
    const customConfig = zoneName
      ? {
          goToEastMY: zoneName === 'EAST',
        }
      : null;
    const { lmd, archive, local_sta, local_std, flight_no, mawbPdfData } =
      await ManifestItemService.generateCustomManifest(
        mawb,
        gaylords,
        parcelClassificationCopy,
        allOpHubs,
        true,
        allConversionRates,
        customBrokerName,
        customConfig,
      );
    const pdfData = await ManifestItemService.generatePdfProformaInvoice(
      parcels,
      allMerchants,
      allOpHubs,
    );

    let proforma_buffer: Buffer;

    if (pdfData.success) {
      proforma_buffer = Buffer.from(JSON.parse(pdfData.buffer));
      archive.push({ name: 'proforma.pdf', buffer: proforma_buffer });
    }

    if (mawbPdfData.success) {
      archive.push({ name: 'mawb.pdf', buffer: mawbPdfData.mawb_buffer });
    }

    const fileName = zoneName
      ? AzureBlobStorage.generateBlobNameWithTimestamp(`${mawb.mawb_no}_${zoneName}.zip`)
      : AzureBlobStorage.generateBlobNameWithTimestamp(`${mawb.mawb_no}.zip`);

    try {
      await AzureBlobStorage.uploadFile(
        await ArchiverService.zip(archive),
        fileName,
        config.azureStorageContainer.MAWB,
      );
    } catch (error) {
      logger.error({ message: `MAWB upload failed for ${mawb.mawb_no}`, error });
    }

    if (!customBroker) {
      throw new Error(`Custom broker not found for LMD ${lmd.lmd_provider_name || lmd.id}`);
    }

    const zipPassword = await CBService.createZipPassword(customBroker.custom_broker_code);
    const zipBuffer = await ArchiverService.zip(archive, zipPassword);

    await EmailService.sendPart2Email(
      customBroker,
      mawb.tracking_status.find((m) => m.tranx_type === EnumMawbTranxType.CREATED),
      flight_no,
      local_sta,
      local_std,
      req,
      parcels,
      zipBuffer,
      zoneName,
      '',
      '',
      '',
      '',
      lmd,
    );
  },

  getManifestItemsByAValue(id: string, compareFields: string[], fields: string[] | string = '*') {
    if (Array.isArray(fields)) {
      fields = fields.map((field) => `c.${field}`);
    }

    const name = '@id';

    if (Array.isArray(compareFields)) {
      compareFields = compareFields.map((field) => `c.${field} = ${name}`);
    }

    const querySpec = {
      query: `SELECT ${fields} FROM c WHERE ${compareFields.join(' OR ')}`,
      parameters: [
        {
          name,
          value: id,
        },
      ],
    };

    return Daos.manifest_items.find(querySpec);
  },

  async getPDReadyToInvoiceParcels(
    merchantName: string,
    fromTime: string | Date,
    toTime: string | Date,
  ) {
    const querySpec: SqlQuerySpec = {
      query: `
        SELECT c.id, c.tracking_id, c.tracking_status, c.PD_invoicing_status, c.order_date,
          c.merchant_order_no, c.service_option, c.shipment_type, c.insurance_amount,
          c.weight_unit, c.weight, c.dimensions_unit, c.length, c.width, c.height, c.item,
          c.country, c.postcode, c.state, c.city_suburb, c.origin_country, c.operation_hub, c.destination_group,
          c.lmd_zone, c.fuel_surcharge, c.sales_tax_currency, c.duty_tax_currency, c.DT_manual_tax, c.incoterm, c.merchant_declared_currency
        FROM c
        WHERE c.merchant_name = @merchantName
          AND IS_DEFINED(c.PD_invoicing_status)
          AND ARRAY_CONTAINS(@statuses, ARRAY_SLICE(c.PD_invoicing_status, -1)[0].status)
          AND (
            (ARRAY_SLICE(c.PD_invoicing_status, -1)[0].timestamp BETWEEN @fromTime AND @toTime)
            OR (ARRAY_SLICE(c.PD_invoicing_status, -1)[0].event_datetime BETWEEN @fromTime AND @toTime)
          )
      `,
      parameters: [
        {
          name: '@merchantName',
          value: AesUtils.CrtCounterEncrypt(merchantName),
        },
        {
          name: '@statuses',
          value: [
            // TODOS: remove reviewed status
            ENUM.invoiceStatus.PD_reviewed_for_invoice,
            ENUM.invoiceStatus.PD_ready_to_invoice,
          ],
        },
        {
          name: '@fromTime',
          value: fromTime as string,
        },
        {
          name: '@toTime',
          value: toTime as string,
        },
      ],
    };

    const parcels = await Daos.manifest_items.find(querySpec);

    for (const parcel of parcels) {
      AesUtils.decryptParcel(parcel);

      parcel.country_ISO2 = CountryService.getCountryCode2(parcel.country);

      if (!parcel.lmd_zone && ['JP', 'KR'].includes(parcel.country_ISO2)) {
        parcel.lmd_zone = parcel.country_ISO2;
      }
    }

    return parcels;
  },

  async getParcelsToRecalculateChargeableWeight(
    merchantAccountNumber: string,
    fromDate: string | Date, // Rate sheet validity from date
    toDate: string | Date, // Rate sheet validity to date
  ): Promise<any> {
    try {
      const querySpec: SqlQuerySpec = {
        query: `
          SELECT 
            c.id, c.merchant_account_number, c.latest_tracking_status, c.tracking_status, c.PD_invoicing_status,
            c.origin_country, c.country, c.service_option, c.lmd_zone,
            c.weight_unit, c.weight, c.dimensions_unit, c.length, c.width, c.height
          FROM c
          WHERE c.merchant_account_number = @merchantAccountNumber
            AND NOT ARRAY_CONTAINS(@statuses, c.latest_tracking_status)
            AND c.tracking_status[0].date >= @fromDate
            AND c.tracking_status[0].date <= @toDate
            AND (
              NOT IS_DEFINED(c.PD_invoicing_status)
              OR NOT ARRAY_CONTAINS(c.PD_invoicing_status,  {status: @PDInvoicingStatus}, true)
            )
        `,
        parameters: [
          {
            name: '@merchantAccountNumber',
            value: merchantAccountNumber,
          },
          {
            name: '@statuses',
            value: [
              CONSTANTS.BOOKING_MONITOR_STATUS.CANCELLED,
              CONSTANTS.BOOKING_MONITOR_STATUS.EXPIRED,
            ],
          },
          {
            name: '@PDInvoicingStatus',
            value: ENUM.invoiceStatus.PD_invoiced,
          },
          {
            name: '@fromDate',
            value: fromDate as string,
          },
          {
            name: '@toDate',
            value: toDate as string,
          },
        ],
      };

      const parcels = await Daos.manifest_items.find(querySpec);

      return parcels.map((parcel) => AesUtils.decryptParcel(parcel));
    } catch (error) {
      logger.error({ error });

      return [];
    }
  },

  async bulkUpdateParcelsAfterApprovedRateSheet(parcels: any, rateSheetId: string) {
    try {
      const { successItems, errorItems } = await Daos.manifest_items.bulkUpdate(parcels);

      for (const { item, error } of [...successItems, ...errorItems]) {
        let message = 'Update parcel success';

        if (error) {
          message = 'Update parcel error';
        }

        message += `, rate_sheet_id=${rateSheetId}`;

        const dataForLogs = parcels.find((parcel) => parcel.id === item.id);

        logger.info({ message, dataForLogs, error });
      }
    } catch (error) {
      logger.error({ error });
    }
  },

  async getMissingPDReadyToInvoiceParcel(merchant, startDate, endDate) {
    logger.info({ message: 'Start query parcels miss pd_ready_to_invoice status' });

    const listParcelMissPDReadyToInvoice = [];
    const querySpec = {
      query: `
          SELECT c.id, c.tracking_id, c.tracking_status, c.order_date,
            c.merchant_order_no, c.service_option, c.shipment_type, c.insurance_amount,
            c.weight_unit, c.weight, c.dimensions_unit, c.length, c.width, c.height,
            c.item, c.country, c.postcode, c.state, c.city_suburb, c.gaylord_no, c.latest_tracking_status,
            c.origin_country, c.operation_hub, c.destination_group, c.lmd_zone, c.fuel_surcharge
          FROM c
          WHERE c.merchant_name = @merchantName
            AND IS_DEFINED(c.gaylord_no)
            AND ARRAY_CONTAINS(@statuses, c.latest_tracking_status)            
            AND (c.tracking_status[0].date BETWEEN @startDate AND @endDate)
            AND (
              NOT IS_DEFINED(c.PD_invoicing_status) 
              OR NOT ARRAY_CONTAINS(c.PD_invoicing_status, {"status": @PDstatus}, true)
            )
        `,
      parameters: [
        {
          name: '@merchantName',
          value: merchant.merchant_name,
        },
        {
          name: '@statuses',
          value: [
            CONSTANTS.BOOKING_MONITOR_STATUS.PACKED_TO_GAYLORD,
            CONSTANTS.BOOKING_MONITOR_STATUS.PACKED_TO_MAWB,
          ],
        },
        {
          name: '@startDate',
          value: startDate,
        },
        {
          name: '@endDate',
          value: endDate,
        },
        {
          name: '@PDstatus',
          value: ENUM.invoiceStatus.PD_ready_to_invoice,
        },
      ],
    };
    const parcels = await Daos.manifest_items.find(querySpec);

    if (parcels.length > 0) {
      const groupParcel = groupBy(parcels, 'gaylord_no');

      for (const gaylordNo of Object.keys(groupParcel)) {
        const gaylord = await GaylordServices.getGaylordsClosedOrReadyToClose(gaylordNo);

        if (gaylord.length > 0) {
          listParcelMissPDReadyToInvoice.push(...groupParcel[`${gaylordNo}`]);
        }
      }
    }

    logger.info({
      message: 'End query parcels miss pd_ready_to_invoice status',
      listParcelMissPDReadyToInvoice,
    });

    return listParcelMissPDReadyToInvoice;
  },

  async getMissingPDAndDTReadyToInvoiceParcel(merchant, startDate, endDate) {
    logger.info({ message: 'Start query parcels miss pd_ready_to_invoice status' });

    const querySpec = {
      query: `
          SELECT c.id, c.tracking_id, c.tracking_status, c.order_date,
            c.merchant_order_no, c.service_option, c.shipment_type, c.insurance_amount,
            c.weight_unit, c.weight, c.dimensions_unit, c.length, c.width, c.height,
            c.item, c.country, c.postcode, c.state, c.city_suburb, c.latest_tracking_status,
            c.origin_country, c.operation_hub, c.destination_group, c.lmd_zone, c.fuel_surcharge
          FROM c
          WHERE c.merchant_name = @merchantName
            AND NOT ARRAY_CONTAINS(@statuses, c.latest_tracking_status)
            AND (c.tracking_status[0].date BETWEEN @startDate AND @endDate)
            AND (
              NOT IS_DEFINED(c.PD_invoicing_status) 
              OR NOT ARRAY_CONTAINS(c.PD_invoicing_status, {"status": @PDstatus}, true)
            )
            AND (
              NOT IS_DEFINED(c.DT_invoicing_status) 
              OR NOT ARRAY_CONTAINS(c.DT_invoicing_status, {"status": @DTstatus}, true)
            )
        `,
      parameters: [
        {
          name: '@merchantName',
          value: merchant.merchant_name,
        },
        {
          name: '@statuses',
          value: [
            CONSTANTS.BOOKING_MONITOR_STATUS.BOOKED,
            CONSTANTS.BOOKING_MONITOR_STATUS.CANCELLED,
            CONSTANTS.BOOKING_MONITOR_STATUS.LMD_RECEIVE,
            CONSTANTS.BOOKING_MONITOR_STATUS.LMD_REJECT,
            CONSTANTS.BOOKING_MONITOR_STATUS.EXPIRED,
            CONSTANTS.BOOKING_MONITOR_STATUS.PICKED_UP,
            CONSTANTS.BOOKING_MONITOR_STATUS.PACKED_TO_GAYLORD,
            CONSTANTS.BOOKING_MONITOR_STATUS.SORTED,
            CONSTANTS.BOOKING_MONITOR_STATUS.RECEIVED_AT_WAREHOUSE,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_CANCELLED,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_DIMS_MISSING,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_HS_DIMS_MISSING,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_HS_MISSING,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_REJECTED_BY_LMD,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_SPLIT_PARCEL,
            CONSTANTS.BOOKING_MONITOR_STATUS.HOLD_PENDING_BOOKING_UPDATES,
            CONSTANTS.BOOKING_MONITOR_STATUS.PENDING_BOOKING_UPDATES,
            CONSTANTS.BOOKING_MONITOR_STATUS.SPLIT_PARCEL_CREATED,
            CONSTANTS.BOOKING_MONITOR_STATUS.SPLIT_PARCEL_COMPLETED_PROCESSING,
            CONSTANTS.BOOKING_MONITOR_STATUS.UPDATE_OF_RECIPIENT_DETAILS,
            CONSTANTS.BOOKING_MONITOR_STATUS.DAMAGE_INTERNAL,
            CONSTANTS.BOOKING_MONITOR_STATUS.ADDRESS_CHANGE,
            CONSTANTS.BOOKING_MONITOR_STATUS.LOST,
            CONSTANTS.BOOKING_MONITOR_STATUS.DAMAGED,
            CONSTANTS.BOOKING_MONITOR_STATUS.TRANSLATED,
            CONSTANTS.BOOKING_MONITOR_STATUS.RELABELLED,
            CONSTANTS.BOOKING_MONITOR_STATUS.REPACKAGED,
            CONSTANTS.BOOKING_MONITOR_STATUS.DISPOSED,
            CONSTANTS.BOOKING_MONITOR_STATUS.RETURNED_TO_MERCHANT,
            CONSTANTS.BOOKING_MONITOR_STATUS.DANGEROUS_GOOD_INTERNAL,
          ],
        },
        {
          name: '@startDate',
          value: startDate,
        },
        {
          name: '@endDate',
          value: endDate,
        },
        {
          name: '@PDstatus',
          value: ENUM.invoiceStatus.PD_ready_to_invoice,
        },
        {
          name: '@DTstatus',
          value: ENUM.invoiceStatus.DT_ready_to_invoice,
        },
      ],
    };

    const parcels = await Daos.manifest_items.find(querySpec);
    logger.info({ message: 'End query parcels miss pd_ready_to_invoice status', parcels });

    return parcels;
  },

  /**
   * Update parcel
   * @param {*} dataUpdate
   */
  async updateParcel(dataUpdate: any) {
    try {
      await Daos.manifest_items.patch(dataUpdate);
    } catch (error) {
      logger.error({ message: 'Update parcel fail', parcelId: dataUpdate.id, error });
    }
  },

  /**
   * Update parcels
   * @param {*} dataUpdate
   */
  async bulkUpdateParcel(dataUpdate: any, fatherFunctionName: string) {
    try {
      const { errorItems } = await Daos.manifest_items.bulkUpdate(dataUpdate);

      for (const errorItem of errorItems)
        logger.error({
          message: `exception when update parcel ${fatherFunctionName}`,
          parcelId: errorItem.item?.id,
          error: errorItem.error,
        });
    } catch (error) {
      logger.error({ message: 'bulkUpdate parcel fail', error });
    }
  },

  /**
   * get booking
   * @param {*} merchantName
   * @param {*} startDate
   * @param {*} endDate
   */
  getParcelsByTimeOfStatus(merchantName: string, startDate: any, endDate: any) {
    const querySpec = {
      query: `SELECT c.id,
                     c.tracking_id,
                     c.order_number,
                     c.lmd,
                     c.merchant_name,
                     c.tracking_status,
                     c.tracking_id_history
                     FROM c WHERE EXISTS 
            ( SELECT VALUE z FROM z in c.tracking_status 
                     WHERE z.date >=  @startDate
                     AND z.date < @endDate) 
                     AND c.merchant_name = @merchantName`,
      parameters: [
        {
          name: '@merchantName',
          value: AesUtils.CrtCounterEncrypt(merchantName),
        },
        {
          name: '@startDate',
          value: startDate,
        },
        {
          name: '@endDate',
          value: endDate,
        },
      ],
    };

    return Daos.manifest_items.find(querySpec);
  },

  async getMerchantByTaxCountry(fields = '*') {
    if (Array.isArray(fields) && fields.length > 0) {
      fields = fields.map((field) => `c.${field}`).join(', ');
    }

    const querySpec = {
      query: `SELECT ${fields} FROM c WHERE IS_DEFINED(c.invoicing_info.tax)`,
    };

    return Daos.merchant.find(querySpec);
  },

  async getDutiesAndTaxesParcels(utcDateTime, startDayCountInvoice, merchant, fields: any = '*') {
    try {
      if (Array.isArray(fields) && fields.length > 0) {
        fields = fields.map((field) => `c.${field}`).join(', ');
      }

      const querySpec = await QueryUtils.generateGetDTInvoicingParcelQuery(
        utcDateTime,
        startDayCountInvoice,
        ENUM.invoiceStatus.DT_ready_to_invoice,
        merchant,
        fields,
      );

      const parcels = await Daos.manifest_items.find(querySpec);

      return parcels.map((parcel) => AesUtils.decryptParcel(parcel));
    } catch (error) {
      logger.error({ error });

      return [];
    }
  },

  // [PD][YY][merchant_account_no][5-digit running no] e.g. PD20US00100001 // old is 19SG00100213
  async getInvoiceNumber(merchantAccountNumber, type) {
    const yy = new Date().getFullYear().toString().slice(2);
    const invoiceNoPrefix = `${type}${yy}${merchantAccountNumber}`;

    const querySpec = {
      query: `SELECT VALUE COUNT(1) FROM c
              WHERE c._partitionKey = @partitionKey AND c.type= @type
                AND STARTSWITH(c.invoiceNumber, @invoiceNoPrefix)`,
      parameters: [
        {
          name: '@invoiceNoPrefix',
          value: invoiceNoPrefix,
        },
        {
          name: '@type',
          value: type === 'PD' ? 'parcelDelivery' : 'dutiesAndTaxesV2',
        },
        {
          name: '@partitionKey',
          value: AesUtils.CrtCounterEncrypt(merchantAccountNumber),
        },
      ],
    };
    const itemCount = await Daos.invoice.find(querySpec);
    const runningNo = `0000${itemCount[0] + 1}`;
    const upcNumber = UpcUtils.generateNumberCheckDigit(
      runningNo.slice(Math.max(0, runningNo.length - 5)),
    );

    return `${invoiceNoPrefix}${upcNumber.slice(Math.max(0, upcNumber.length - 5))}`;
  },

  async triggerLmdBooking(parcel: any, triggerPoint: string) {
    if (
      !(await MerchantService.checkIsMerchantInCategoryOne(
        AesUtils.CrtCounterDecrypt(parcel.merchant_name),
      ))
    ) {
      return;
    }

    const parcelHasDimWeight = parcel.width && parcel.weight && parcel.height && parcel.length;
    const lmdReceivedStt = StatusMappingService.getManifestStatus(
      ENUM.parcelStatus.lmd_receive_booking,
    );
    const lmdRejectedStt = StatusMappingService.getManifestStatus(
      ENUM.parcelStatus.lmd_reject_booking,
    );

    if (
      parcel.tracking_status?.some(({ status }) =>
        [lmdReceivedStt, lmdRejectedStt].includes(status),
      )
    ) {
      logger.info({
        message: 'Parcel latest status not able to be queue for booking',
        parcelId: parcel.id,
        parcelTrackingStatus: parcel.tracking_status,
      });

      return;
    }

    try {
      if (!parcelHasDimWeight) {
        logger.info({
          message: 'Parcel missing dims weight, skip rebook parcel',
          parcelId: parcel.id,
        });

        return;
      }

      if (parcel.lmd === ENUM.lmdNames.NinjavanSG || parcel.has_hscode) {
        await AzureStorageQueue.sendBase64Message(CONSTANTS.AZURE_STORAGE_QUEUE_NAME.LMD_BOOKING, {
          parcelId: parcel.id,
          triggerPoint,
        });
        logger.info({ message: `Trigger LMD booking for parcelId ${parcel.id}` });
      } else {
        logger.info({ message: 'Parcel missing HS code, skip rebook parcel', parcelId: parcel.id });
      }
    } catch (error) {
      logger.error({
        message: `error while trigger lmd booking for parcelId ${parcel.id}`,
        triggerPoint,
        error,
      });
    }
  },

  mapUploadDimsWeightErrors(csvData: any, errors: any) {
    const errorMap = new Map();

    for (const errorType in errors) {
      if (Object.hasOwn(errors, errorType)) {
        const error = errors[errorType];

        for (const id of error.data) {
          if (!errorMap.has(id)) {
            errorMap.set(id, []);
          }

          errorMap.get(id).push(error.message);
        }
      }
    }

    for (const parcel of csvData) {
      parcel.error_message = errorMap.get(parcel.shipment_tracking_id)?.join(', ') || '';
    }

    return csvData;
  },

  async notifyDimsWeightUploadResult(user: any, errorItems: any) {
    if (errorItems.length > 0) {
      const errorsCsvFields = [
        {
          label: 'Tracking ID',
          value: 'trackingId',
        },
        {
          label: 'Error',
          value: 'error',
        },
      ];
      const errorsCSVParser = new Json2csvParser({ fields: errorsCsvFields });
      const errorsCSVBuffer = Buffer.from(errorsCSVParser.parse(errorItems));

      await EmailService.sendParcelsDimsWeightUpdateResult(user.email, user.name, errorsCSVBuffer);
    } else {
      await EmailService.sendParcelsDimsWeightUpdateResult(user.email, user.name);
    }
  },

  async getParcelDamagedPic(id: string) {
    const querySpec = {
      query: `SELECT c.id, c.tracking_id FROM c WHERE c.id = @id OR c.tracking_id = @id`,
      parameters: [
        {
          name: '@id',
          value: id,
        },
      ],
    };

    const [parcelRecord] = await Daos.manifest_items.find(querySpec);

    if (!parcelRecord) return;

    const latestBlobs = await AzureBlobStorage.getLatestFilesByTimestamp(
      [parcelRecord.id, parcelRecord.tracking_id],
      EXT.JPG,
      config.azureStorageContainer.DAMAGED_PARCEL_IMAGES,
    );
    const images = await Promise.all(
      latestBlobs.map((blob) =>
        AzureBlobStorage.downloadBlobBuffer(
          config.azureStorageContainer.DAMAGED_PARCEL_IMAGES,
          blob.name,
        ),
      ),
    );

    return images[0] || images[1];
  },

  /**
   * Get all related cancellable parcels for the given parcel.
   */
  async getCancellableMultiPieceParcels(id: string, parentId: string) {
    if (!parentId) {
      return {
        data: [],
        isSplitCompleted: false,
      };
    }

    try {
      if (id === parentId) {
        // If given parcel is parent -> return all cancellable children
        const cancellableChildParcels = await this.filterCancellableParcels(parentId, true);

        logger.info({
          message: `Cancel all ${cancellableChildParcels.parcels.length} cancellable child parcels for ${id}`,
          'Multi-Piece Shipment': `${id} from payload is parent`,
        });

        return {
          data: cancellableChildParcels.parcels,
          isParent: true,
          isSplitCompleted: cancellableChildParcels.isSplitCompleted,
        };
      }

      // If given parcel is the last child that is eligible for cancellation -> return parent
      const cancellableParent = await this.filterCancellableParcels(parentId, false);

      logger.info({ message: `Multi-Piece Shipment: ${id} from payload is child` });

      return {
        data: cancellableParent.parcels,
        isSplitCompleted: cancellableParent.isSplitCompleted,
      };
    } catch (error) {
      logger.error({ message: `Error getting cancellable multi piece parcels`, error });

      return {
        data: [],
        isSplitCompleted: false,
      };
    }
  },

  async filterCancellableParcels(parentId: string, isGivenParent: boolean) {
    const [cancelledStt, holdCancelledStt] = StatusMappingService.getMultipleManifestStatus([
      ENUM.parcelStatus.cancelled,
      ENUM.parcelStatus.hold_cancelled,
    ]);
    const querySpec: SqlQuerySpec = isGivenParent
      ? {
          query: `SELECT
            c.id,
            c.parent_id,
            c.tracking_id,
            c.latest_tracking_status,
            c.PD_invoicing_status,
            c.lmd
          FROM c
          WHERE c.parent_id = @parentId
          AND c.parent_id != c.id
          AND NOT ARRAY_CONTAINS(@cancelledStatus, c.latest_tracking_status)`,
          parameters: [
            {
              name: '@parentId',
              value: parentId,
            },
            {
              name: '@cancelledStatus',
              value: [cancelledStt, holdCancelledStt],
            },
          ],
        }
      : {
          query: `SELECT
            c.id,
            c.parent_id,
            c.tracking_id,
            c.latest_tracking_status,
            c.PD_invoicing_status,
            c.lmd
          FROM c
          WHERE c.parent_id = @parentId
          AND NOT ARRAY_CONTAINS(@cancelledStatuses, c.latest_tracking_status)`,
          parameters: [
            {
              name: '@parentId',
              value: parentId,
            },
            {
              name: '@cancelledStatuses',
              value: [cancelledStt, holdCancelledStt],
            },
          ],
        };
    const notCancelledParcels = await Daos.manifest_items.find(querySpec);
    const notPdReadyParcels = notCancelledParcels.filter((parcel) => {
      return !parcel.PD_invoicing_status?.find(
        (invoicingObj: { status: string; timestamp: Date | string }) =>
          invoicingObj.status === ENUM.invoiceStatus.PD_ready_to_invoice,
      );
    });
    let cancellableParcels = notPdReadyParcels.filter((parcel) =>
      StatusMappingService.isCancellable(parcel.latest_tracking_status),
    );

    if (!isGivenParent) {
      let cancellableParent = [];
      const notCancelledChildParcel = cancellableParcels.filter(
        (parcel) => parcel.id !== parcel.parent_id,
      );

      if (notCancelledChildParcel.length === 1) {
        cancellableParent = notPdReadyParcels.filter((parcel) => parcel.id === parcel.parent_id);
      }

      cancellableParcels = [...cancellableParent];
    }

    return {
      parcels: cancellableParcels,
      isSplitCompleted: isGivenParent
        ? cancellableParcels.length < notCancelledParcels.length
        : cancellableParcels.length > 0,
    };
  },

  async getCancellableParcelsByParentId(parentId: string) {
    const cancelledStatuses = StatusMappingService.getMultipleManifestStatus([
      ENUM.parcelStatus.cancelled,
      ENUM.parcelStatus.hold_cancelled,
    ]);
    const querySpec = {
      query: `SELECT
            c.id,
            c.parent_id,
            c.tracking_id,
            c.latest_tracking_status,
            c.lmd,
            c.PD_invoicing_status
          FROM c
          WHERE c.parent_id = @parentId
          AND NOT ARRAY_CONTAINS(@cancelledStatus, c.latest_tracking_status)`,
      parameters: [
        {
          name: '@parentId',
          value: parentId,
        },
        {
          name: '@cancelledStatus',
          value: cancelledStatuses,
        },
      ],
    };
    const notCancelledParcels = await Daos.manifest_items.find(querySpec);
    const notPdReadyParcels = notCancelledParcels.filter((parcel) => {
      return !parcel.PD_invoicing_status?.some(
        (invoicingObj: { status: string; timestamp: Date | string }) =>
          invoicingObj.status === ENUM.invoiceStatus.PD_ready_to_invoice,
      );
    });

    return {
      data: notPdReadyParcels,
      isSplitCompleted: notPdReadyParcels.length < notCancelledParcels.length,
    };
  },

  async getParcelForCommercialInvoice(
    gaylordIds: string[],
    acceptStatus: string[],
    rejectStatus: string[],
    originCountry: string,
  ) {
    const parcelQuery: SqlQuerySpec = {
      query:
        'SELECT c as parcel FROM c JOIN s IN c.tracking_status WHERE ARRAY_CONTAINS(@gaylordNos, c.gaylord_no)' +
        ' AND c.shipment_type = @shipmentType' +
        ' AND ARRAY_CONTAINS(@requiredStatus, s.status)' +
        ' AND NOT ARRAY_CONTAINS(@excludedStatus, s.status)' +
        ' AND c.origin_country = @originCountry',
      parameters: [
        {
          name: '@gaylordNos',
          value: gaylordIds,
        },
        {
          name: '@shipmentType',
          value: 'international',
        },
        {
          name: '@requiredStatus',
          value: acceptStatus,
        },
        {
          name: '@excludedStatus',
          value: rejectStatus,
        },
        {
          name: '@originCountry',
          value: originCountry,
        },
      ],
    };

    const parcels = [];
    const merchantNames = [];
    const opsHubNames = [];

    const data = await Daos.manifest_items.find(parcelQuery);

    for (const parcelObj of data) {
      const { parcel } = parcelObj;

      if (parcel.merchant_name && !merchantNames.includes(parcel.merchant_name)) {
        merchantNames.push(parcel.merchant_name);
      }

      if (parcel.operation_hub && !opsHubNames.includes(parcel.operation_hub)) {
        opsHubNames.push(parcel.operation_hub);
      }

      parcels.push(AesUtils.decryptParcel(parcel));
    }

    return [parcels, merchantNames, opsHubNames];
  },

  async getParcelsInHawb(hawb: any) {
    const parcelsQuery = {
      query: `SELECT * from c 
              where c.service_option = 'b2b' and c.merchant_name = @merchantName 
              and c.mawb_no = @mawbNumber`,
      parameters: [
        {
          name: '@merchantName',
          value: hawb.merchant_name,
        },
        {
          name: '@mawbNumber',
          value: hawb.mawb_no,
        },
      ],
    };

    const parcels = await Daos.manifest_items.find(parcelsQuery);

    return parcels;
  },
};

/**
 * Get destination country.
 * @param {Array<>} allDestinations
 * @param {Array<>} gaylord
 * @returns return destination object with modified fields.
 */
function getDestByMawb(allDestinations: any[], gaylord: any) {
  const lmd = allDestinations.find(
    (item) =>
      item.item_type === ENUM.destinationGroupType.LMD && item.id === gaylord.lmd_provider_name,
  );

  if (lmd) {
    const destination = allDestinations.find(
      (item) =>
        item.item_type === ENUM.destinationGroupType.COUNTRY && item.country === lmd.country,
    );
    destination.lmds = [lmd];

    return destination;
  }

  return null;
}
