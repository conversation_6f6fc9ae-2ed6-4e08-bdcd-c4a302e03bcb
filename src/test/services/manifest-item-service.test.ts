import axios from 'axios';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';
import { ReportUtils } from '~/models/reportUtils.js';

import { ArchiverService } from '~/services/archiver.service.js';
import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { EmailService } from '~/services/email.services.js';
import { GaylordServices } from '~/services/gaylord.service.js';
import { HawbService } from '~/services/hawb.service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { MawbService } from '~/services/mawb-service.js';
import { StatusService } from '~/services/status_service.js';
import StatusMappingService from '~/services/statusMappingService.js';
import { SystemConfigurationsService } from '~/services/systemConfigurationsService.js';

import { PlsReportUtils } from '~/utilities/plsReportUtils.js';
import { ShipmentUtils } from '~/utilities/shipmentUtils.js';

import { Daos } from '~/daos/index.js';

import parcelHistoryService from '../../main/services/parcel-history.service.js';

Daos.manifest_items.find = vi.fn();
Daos.manifest_items.updateItemResolveConflict = vi.fn();

beforeEach(() => {
  vi.clearAllMocks();
  Daos.manifest_items.find = vi.fn();
  Daos.manifest_items.updateItemResolveConflict = vi.fn();

  // Reset HawbService mock
  HawbService.getHawbById = vi.fn();
});

console.log = vi.fn();
console.info = vi.fn();
console.error = vi.fn();

describe('test ManifestItemService :', () => {
  describe('test generateCustomManifest', () => {
    test('should return create archive', async () => {
      // Assign
      const customBrokerName = '';
      const mawb = {
        id: '123',
        tracking_status: [
          {
            type: '',
            latest_tracking_status: 'FINISH',
            second_sector: {
              flight_date: '2023-08-18',
            },
          },
        ],
      };
      const gaylords = [
        {
          lmd_provider_name: 'abc',
        },
      ];
      const classData = {
        parcelsAbove: ['above'],
        parcelsBelow: ['below'],
        allParcels: [
          {
            id: 'above',
            operation_hub: 'SIN01',
          },
          {
            id: 'below',
            operation_hub: 'SIN01',
          },
        ],
        destination: {
          country: 'MY',
          lmds: [
            {
              id: gaylords[0].lmd_provider_name,
            },
          ],
        },
      };
      const allOpHubs = [{ operation_hub: 'SIN01' }];
      const sendSftp = false;
      const allConversionRates = [];
      SystemConfigurationsService.getConfigByName = vi.fn().mockReturnValue('V2');
      ReportUtils.export_custom_broker_manifest_MY_Template = vi.fn().mockResolvedValue('dfasdf');
      // Act
      const result = await ManifestItemService.generateCustomManifest(
        mawb,
        gaylords,
        classData,
        allOpHubs,
        sendSftp,
        allConversionRates,
        customBrokerName,
      );
      // Assert
      expect(result.archive).toEqual(expect.any(Object));
    });

    test('should test NZ case', async () => {
      // Assign
      const customBrokerName = '';
      const mawb = {
        id: '123',
        tracking_status: [
          {
            type: '',
            latest_tracking_status: 'FINISH',
            second_sector: {
              flight_date: '2023-08-18',
            },
          },
        ],
      };
      const gaylords = [
        {
          lmd_provider_name: 'abc',
        },
      ];
      const classData = {
        parcelsAbove: ['above'],
        parcelsBelow: ['below'],
        allParcels: [
          {
            id: 'above',
            operation_hub: 'SIN01',
          },
          {
            id: 'below',
            operation_hub: 'SIN01',
          },
        ],
        destination: {
          country: 'NZ',
          currency: 'NZD',
          lmds: [
            {
              id: gaylords[0].lmd_provider_name,
            },
          ],
        },
      };
      const allOpHubs = [{ operation_hub: 'SIN01' }];
      const sendSftp = false;
      const allConversionRates = [];
      SystemConfigurationsService.getConfigByName = vi.fn().mockReturnValue('V2');
      ReportUtils.custom_broker_manifest_NZ_template = vi.fn().mockReturnValue('dfasdf');
      CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValueOnce({
        destination_currency: 2,
      });
      // Act
      const result = await ManifestItemService.generateCustomManifest(
        mawb,
        gaylords,
        classData,
        allOpHubs,
        sendSftp,
        allConversionRates,
        customBrokerName,
      );
      // Assert
      expect(result.archive).toEqual(expect.any(Object));
    });
  });

  describe('it should test getSumOfAllSubtotalDeclaredCIFValueByTrackingId as expected', () => {
    const tracking_id = 'TKID_1234';
    const items = [
      {
        seq_no: '1',
        description: 'Clothes',
        quantity: '2',
        SKU: '123456789',
        hs_code: '6210',
        subtotal_weight: '3.3',
        total_declared_value: '35.65',
      },
      {
        seq_no: '2',
        description: 'Clothes',
        quantity: '2',
        SKU: '123456789',
        hs_code: '6210',
        subtotal_weight: '3.1',
        total_declared_value: '35.65',
      },
    ];

    const items2 = [
      {
        seq_no: '1',
        description: 'Clothes',
        quantity: '2',
        SKU: '123456789',
        hs_code: '6210',
        subtotal_weight: '3.3',
      },
      {
        seq_no: '2',
        description: 'Clothes',
        quantity: '2',
        SKU: '123456789',
        hs_code: '6210',
        subtotal_weight: '3.1',
      },
    ];

    const sum = 35.65 * 2;

    test('test getSumOfAllSubtotalDeclaredCIFValueByTrackingId as expected', async () => {
      Daos.manifest_items.find = vi.fn().mockResolvedValueOnce([{ item: items }]);
      const result =
        await ManifestItemService.getSumOfAllSubtotalDeclaredCIFValueByTrackingId(tracking_id);
      expect(result).toEqual(sum);
    });

    test('test getSumOfAllSubtotalDeclaredCIFValueByTrackingId as expected', async () => {
      Daos.manifest_items.find = vi.fn().mockResolvedValueOnce([]);
      const result =
        await ManifestItemService.getSumOfAllSubtotalDeclaredCIFValueByTrackingId(tracking_id);
      expect(result).toEqual(0);
    });

    test('test getSumOfAllSubtotalDeclaredCIFValueByTrackingId as expected', async () => {
      Daos.manifest_items.find = vi.fn().mockResolvedValueOnce([{ item: items2 }]);
      const result =
        await ManifestItemService.getSumOfAllSubtotalDeclaredCIFValueByTrackingId(tracking_id);
      expect(result).toEqual(0);
    });

    test('test getSumOfAllSubtotalDeclaredCIFValueByTrackingId return error as expected', async () => {
      Daos.manifest_items.find = vi.fn().mockRejectedValueOnce({ err: 'error' });

      try {
        await ManifestItemService.getSumOfAllSubtotalDeclaredCIFValueByTrackingId(tracking_id);
      } catch (error) {
        expect(error).toEqual({ err: 'error' });
      }
    });
  });

  describe('test getParcelsByGlId func', () => {
    test('it should return correct data', async () => {
      // Arrange
      Daos.manifest_items.find = vi.fn().mockResolvedValueOnce([{ id: 'SQSG112233540162' }]);
      // Act
      const data = await ManifestItemService.getParcelsByGlId('GL-20-00000066', ['id']);
      // Assert
      expect(data).toEqual([{ id: 'SQSG112233540162' }]);
    });
  });
  describe('test updateMerchantDeclaredValue func', () => {
    const parcelMock = {
      id: 'SQSG112233540162',
      converted_merchant_declared_value: 100,
    };
    test('when error occurs, it should log the err', async () => {
      // Arrange
      Daos.manifest_items.updateItemResolveConflict = vi.fn().mockRejectedValue('err');

      // Act
      try {
        await ManifestItemService.updateMerchantDeclaredValue(parcelMock);
      } catch {
        // Assert
        expect(console.error).toHaveBeenCalledWith(
          'type=ERROR, name=ManifestItemService, function=updateMerchantDeclaredValue, parcelId=SQSG112233540162, error=err',
        );
      }
    });
    test('when updating successfully, it should log the message', async () => {
      // Arrange
      Daos.manifest_items.updateItemResolveConflict = vi.fn().mockResolvedValue('successfully');
      // Act
      await ManifestItemService.updateMerchantDeclaredValue(parcelMock);
      // Assert
      expect(console.info).toHaveBeenCalledWith(
        'type=INFO, name=ManifestItemService, function=updateMerchantDeclaredValue, message=parcelClassification Updated Successfully-converted_merchant_declared_value, parcelId=SQSG112233540162, parcelConvertedMerchantDeclaredValue=100',
      );
    });
  });
  describe('test generatePdfProformaInvoice func', () => {
    const parcelsMock = [{ id: 'SQSG112233540162' }];
    const merchantsMock = [{ id: 'SQSG112233540162' }];
    const opHubsMock = [{ id: 'SIN' }];
    test('when inputs are all blank, return the error', async () => {
      // Act
      const result = await ManifestItemService.generatePdfProformaInvoice([], [], []);
      // Assert
      expect(result).toEqual({
        message: 'blank input',
        buffer: '',
        success: false,
      });
    });
    test('when generating invoice successfully, return the data', async () => {
      // Arrange
      const buffer = Buffer.from('test buffer');
      SystemConfigurationsService.getConfigByName = vi.fn().mockReturnValue('V2');
      axios.post = vi.fn().mockResolvedValue({ data: buffer });
      // Act
      const result = await ManifestItemService.generatePdfProformaInvoice(
        parcelsMock,
        merchantsMock,
        opHubsMock,
      );
      // Assert
      expect(result).toEqual({
        type: 'Buffer convert to JSON',
        buffer: JSON.stringify(buffer),
        success: true,
      });
    });
    test('when generating invoice fail, return the error message', async () => {
      // Arrange
      SystemConfigurationsService.getConfigByName = vi.fn().mockReturnValue('V2');
      axios.post = vi.fn().mockRejectedValueOnce('err');

      // Act
      try {
        await ManifestItemService.generatePdfProformaInvoice(
          parcelsMock,
          merchantsMock,
          opHubsMock,
        );
      } catch (error) {
        // Assert
        expect(error).toBe('err');
      }
    });
  });
  describe('test getLmdReceivedBookings func', () => {
    test('it should return correct data', async () => {
      // Arrange
      Daos.manifest_items.find = vi
        .fn()
        .mockResolvedValue([{ destination_group: 'VIE' }, { destination_group: 'VIE' }]);
      // Act
      const data = await ManifestItemService.getLmdReceivedBookings('SIN', '13 Mar', '15 Mar', []);
      // Assrt
      expect(data).toEqual({ VIE: 2 });
    });
  });
  describe('test  getParcelsByParcelNumberOrTrackingId func', () => {
    test('when an error occur, return an empty array', async () => {
      // Arrange
      Daos.manifest_items.find = vi.fn().mockImplementation(() => {
        throw 'err';
      });
      // Act
      const result =
        await ManifestItemService.getParcelsByParcelNumberOrTrackingId('SQSG112233540162');
      // Assert
      expect(result).toEqual([]);
    });
    test('when no error occur, return correct data', async () => {
      // Arrange
      Daos.manifest_items.find = vi.fn().mockImplementation(() => ['data']);
      // Act
      const result =
        await ManifestItemService.getParcelsByParcelNumberOrTrackingId('SQSG112233540162');
      // Assert
      expect(result).toEqual(['data']);
    });
  });
  describe('test getParcelInfoForReportPage', () => {
    test('should return parcel info with tracking statuses properly processed', async () => {
      // Arrange
      const searchValue = 'TRACKING123';
      const mockParcel = {
        id: 'PARCEL123',
        tracking_id: 'TRACKING123',
        parent_id: 'PARENT123',
        gaylord_no: 'GAYLORD123',
        tracking_status: [
          { status: 'Booked', date: '2023-01-01T00:00:00Z', description: 'Booked' },
          { status: 'Shipped', date: '2023-01-02T00:00:00Z', description: 'Shipped' },
        ],
      };

      const mockGaylord = {
        overpack_id: 'OVERPACK123',
        mawb_no: 'MAWB123',
        tracking_status: [{ status: 'Gaylord Created', date: '2023-01-01T00:00:00Z' }],
      };

      const mockMawb = {
        id: 'MAWB123',
        tracking_status: [{ status: 'STASTD', date: '2023-01-03T00:00:00Z' }],
      };

      const mockChildrenParcels = [{ id: 'CHILD1' }, { id: 'PARCEL123' }, { id: 'CHILD2' }];

      const mockUpdateHistory = [{ id: 'HISTORY1', timestamp: '2023-01-01T00:00:00Z' }];

      // Mock status service responses
      const mockExceptionStatuses = ['Exception1', 'Exception2'];
      const mockWarehouseStatuses = { Warehouse1: null, Warehouse2: null };
      const mockFlightStatuses = { Flight1: null, Flight2: null };
      const mockStatusNeedLastTimestamp = ['Status1', 'Status2'];

      // Mock the parcelStatusMapping for sequence
      Object.defineProperty(StatusMappingService, 'parcelStatusMapping', {
        value: {
          Booked: { manifestStt: 'Booked', sequence: 1 },
          Shipped: { manifestStt: 'Shipped', sequence: 2 },
          LMD1: { manifestStt: 'LMD Status 1', sequence: 3 },
          LMD2: { manifestStt: 'LMD Status 2', sequence: 4 },
        },
        configurable: true,
      });

      // Setup mocks
      Daos.manifest_items.find = vi.fn().mockResolvedValue([mockParcel]);
      GaylordServices.getGaylordById = vi.fn().mockResolvedValue(mockGaylord);
      MawbService.getMawb = vi.fn().mockResolvedValue(mockMawb);
      ManifestItemService.getAllFellowChildrenParcel = vi
        .fn()
        .mockResolvedValue(mockChildrenParcels);
      parcelHistoryService.getChangeOfBookingHistory = vi.fn().mockResolvedValue(mockUpdateHistory);
      parcelHistoryService.getChangeOfBookingUpdateHistoryInSequence = vi
        .fn()
        .mockReturnValue(mockUpdateHistory);

      StatusService.getExceptionStatusForPLSSearch = vi.fn().mockReturnValue(mockExceptionStatuses);
      StatusService.getWarehouseStatusForPLSSearch = vi.fn().mockReturnValue(mockWarehouseStatuses);
      StatusService.getFlightStatusForPLSSearch = vi.fn().mockReturnValue(mockFlightStatuses);
      StatusService.getStatusNeedLastTimetamp = vi
        .fn()
        .mockReturnValue(mockStatusNeedLastTimestamp);

      PlsReportUtils.combineStatusesForPLSSearch = vi.fn().mockReturnValue([
        {
          status: 'Booked',
          date: '2023-01-01T00:00:00Z',
          receiveStatusDate: '2023-01-01T00:00:00Z',
          description: 'Booked',
          statusLocation: 'Location1',
        },
        {
          status: 'Shipped',
          date: '2023-01-02T00:00:00Z',
          receiveStatusDate: '2023-01-02T00:00:00Z',
          description: 'Shipped',
          statusLocation: 'Location2',
        },
        {
          status: 'LMD Status 1',
          date: '2023-01-04T00:00:00Z',
          receiveStatusDate: '2023-01-04T00:00:00Z',
          description: 'LMD Status 1',
          statusLocation: 'Location3',
        },
      ]);

      ShipmentUtils.setAttemptedDeliveryToParcel = vi.fn();
      AesUtils.decryptParcel = vi.fn().mockImplementation((parcel) => parcel);

      // Mock B2B processing (should not be called for non-B2B parcel)
      ManifestItemService.processB2BHawbInfo = vi.fn().mockImplementation((parcel) => parcel);

      // Act
      const result = await ManifestItemService.getParcelInfoForReportPage(searchValue);

      // Assert
      expect(Daos.manifest_items.find).toHaveBeenCalledWith(expect.any(Object));
      expect(GaylordServices.getGaylordById).toHaveBeenCalledWith('GAYLORD123');
      expect(MawbService.getMawb).toHaveBeenCalledWith('MAWB123');
      expect(ManifestItemService.getAllFellowChildrenParcel).toHaveBeenCalledWith('PARENT123', [
        'id',
      ]);
      expect(ManifestItemService.processB2BHawbInfo).toHaveBeenCalledWith(expect.any(Object));

      // Check that the result has the expected properties
      expect(result).toBeDefined();
      expect(result.mawb_no).toBe('MAWB123');
      expect(result.overpack_id).toBe('OVERPACK123');
      expect(result.children_parcels).toEqual(['CHILD1', 'CHILD2']);
      expect(result.tracking_status_merged).toBeDefined();
      expect(result.lastMileStatus).toBeDefined();
      expect(result.warehouseStatus).toBeDefined();
      expect(result.flightStatus).toBeDefined();
      expect(result.updateHistory).toEqual(mockUpdateHistory);

      // Verify helper functions were called
      expect(ShipmentUtils.setAttemptedDeliveryToParcel).toHaveBeenCalledWith(
        result,
        StatusMappingService,
      );
    });

    test('should return undefined when no parcels found', async () => {
      // Arrange
      Daos.manifest_items.find = vi.fn().mockResolvedValue([]);

      // Act
      const result = await ManifestItemService.getParcelInfoForReportPage('NOTFOUND');

      // Assert
      expect(result).toBeUndefined();
    });

    test('should log error when exception occurs', async () => {
      // Arrange
      const mockError = new Error('Test error');
      Daos.manifest_items.find = vi.fn().mockRejectedValue(mockError);
      console.error = vi.fn();

      // Act
      await ManifestItemService.getParcelInfoForReportPage('ERROR123');

      // Assert
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('test processB2BHawbInfo', () => {
    test('should enhance B2B shipment with HAWB PD data', async () => {
      // Arrange
      const mockB2BParcel = {
        id: 'PARCEL123',
        service_option: 'b2b',
        hawb_id: 'HAWB123',
        mawb_no: 'GAYLORD_MAWB',
        DT_invoicing_status: [
          { status: 'DT ready to invoice', timestamp: '2023-01-01' },
        ],
      };

      const mockHawb = {
        id: 'HAWB123',
        mawb_no: 'HAWB_MAWB',
        hawb_no: 'HAWB_USER_123',
        pd_invoicing_status: [
          { status: 'PD ready to invoice', date: new Date('2023-01-01') },
          { status: 'PD invoiced', date: new Date('2023-01-02') },
        ],
        pd_amount: 100.5,
        pd_currency: 'USD',
        pd_invoice_no: 'INV123',
      };

      HawbService.getHawbById = vi.fn().mockResolvedValue(mockHawb);

      // Act
      const result = await ManifestItemService.processB2BHawbInfo(mockB2BParcel);

      // Assert
      expect(HawbService.getHawbById).toHaveBeenCalledWith('HAWB123');
      expect(result.mawb_no).toBe('HAWB_MAWB'); // Should override gaylord value
      expect(result.hawb_id).toBe('HAWB123');
      expect(result.hawb_no).toBe('HAWB_USER_123');
      expect(result.pd_invoicing_statuses).toHaveLength(2);
      expect(result.latest_pd_invoicing_status).toBe('PD invoiced');
      expect(result.pd_amount).toBe(100.5);
      expect(result.pd_currency).toBe('USD');
      expect(result.pd_invoice_no).toBe('INV123');

      // DT statuses should remain unchanged (already in manifest item)
      expect(result.DT_invoicing_status).toEqual([
        { status: 'DT ready to invoice', timestamp: '2023-01-01' },
      ]);
    });

    test('should handle non-B2B shipments without modification', async () => {
      // Arrange
      const mockStandardParcel = {
        id: 'PARCEL123',
        service_option: 'standard',
        mawb_no: 'ORIGINAL_MAWB',
      };

      // Act
      const result = await ManifestItemService.processB2BHawbInfo(mockStandardParcel);

      // Assert
      expect(result).toEqual(mockStandardParcel);
      expect(HawbService.getHawbById).not.toHaveBeenCalled();
    });

    test('should handle B2B shipments without hawb_id gracefully', async () => {
      // Arrange
      const mockB2BParcelNoHawb = {
        id: 'PARCEL123',
        service_option: 'b2b',
        // No hawb_id
        mawb_no: 'ORIGINAL_MAWB',
      };

      // Act
      const result = await ManifestItemService.processB2BHawbInfo(mockB2BParcelNoHawb);

      // Assert
      expect(result).toEqual(mockB2BParcelNoHawb);
      expect(HawbService.getHawbById).not.toHaveBeenCalled();
    });

    test('should handle HAWB retrieval errors gracefully', async () => {
      // Arrange
      const mockB2BParcel = {
        id: 'PARCEL123',
        service_option: 'b2b',
        hawb_id: 'HAWB123',
        mawb_no: 'ORIGINAL_MAWB',
      };

      HawbService.getHawbById = vi.fn().mockRejectedValue(new Error('HAWB not found'));

      // Act
      const result = await ManifestItemService.processB2BHawbInfo(mockB2BParcel);

      // Assert
      expect(result.mawb_no).toBe('ORIGINAL_MAWB'); // Should keep original
      expect(result.hawb_id).toBe('HAWB123'); // hawb_id should remain from input
      expect(result.pd_invoicing_statuses).toBeUndefined();
    });

    test('should handle missing HAWB data gracefully', async () => {
      // Arrange
      const mockB2BParcel = {
        id: 'PARCEL123',
        service_option: 'b2b',
        hawb_id: 'HAWB123',
        mawb_no: 'ORIGINAL_MAWB',
      };

      HawbService.getHawbById = vi.fn().mockResolvedValue(null);

      // Act
      const result = await ManifestItemService.processB2BHawbInfo(mockB2BParcel);

      // Assert
      expect(result.mawb_no).toBe('ORIGINAL_MAWB'); // Should keep original
      expect(result.hawb_id).toBe('HAWB123'); // hawb_id should remain from input
      expect(result.pd_invoicing_statuses).toBeUndefined();
    });
  });

  describe('test getLatestPDInvoicingStatus', () => {
    test('should return latest status by date', () => {
      // Arrange
      const statuses = [
        { status: 'PD ready to invoice', date: new Date('2023-01-01') },
        { status: 'PD invoiced', date: new Date('2023-01-03') },
        { status: 'PD reviewed for invoice', date: new Date('2023-01-02') },
      ];

      // Act
      const result = ManifestItemService.getLatestPDInvoicingStatus(statuses);

      // Assert
      expect(result).toBe('PD invoiced');
    });

    test('should return undefined for empty array', () => {
      // Act
      const result = ManifestItemService.getLatestPDInvoicingStatus([]);

      // Assert
      expect(result).toBeUndefined();
    });

    test('should return undefined for null input', () => {
      // Act
      const result = ManifestItemService.getLatestPDInvoicingStatus(null);

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('test groupDateBaseOnStatus', () => {
    test('should pass', () => {
      // Act
      const result = ManifestItemService.groupDateBaseOnStatus([
        {
          status: 'status',
          date: '2022-04-15T04:00:00.000Z',
        },
        {
          status: 'status',
          date: '2022-04-14T04:00:00.000Z',
        },
        {
          status: 'status1',
          date: '2022-04-15T04:00:00.000Z',
        },
      ]);
      // Assert
      expect(result).toEqual([
        {
          date: ['2022-04-14T04:00:00.000Z', '2022-04-15T04:00:00.000Z'],
          status: 'status',
        },
        {
          date: ['2022-04-15T04:00:00.000Z'],
          status: 'status1',
        },
      ]);
    });

    test('should fail', () => {
      // Act
      const result = ManifestItemService.groupDateBaseOnStatus(undefined);
      // Assert
      expect(result).toEqual(new Error('manifest-item.service Error: invalid statusGroups'));
    });
  });

  describe('test groupDateStatusPLSSearch', () => {
    test('should pass', () => {
      // Act
      const result = ManifestItemService.groupDateStatusPLSSearch([
        {
          status: 'status',
          date: '2022-04-15T04:00:00.000Z',
          receiveStatusDate: '2022-04-15T04:00:00.000Z',
        },
        {
          status: 'status',
          date: '2022-04-14T04:00:00.000Z',
          receiveStatusDate: '2022-04-14T04:00:00.000Z',
        },
        {
          status: 'status1',
          date: '2022-04-15T04:00:00.000Z',
          receiveStatusDate: '2022-04-15T04:00:00.000Z',
        },
      ]);
      // Assert
      expect(result).toEqual([
        {
          statusGrouped: [
            {
              date: '2022-04-14T04:00:00.000Z',
              description: undefined,
              statusLocation: undefined,
              receiveStatusDate: '2022-04-14T04:00:00.000Z',
            },
            {
              date: '2022-04-15T04:00:00.000Z',
              receiveStatusDate: '2022-04-15T04:00:00.000Z',
              description: undefined,
              statusLocation: undefined,
            },
          ],
          status: 'status',
        },
        {
          statusGrouped: [
            {
              date: '2022-04-15T04:00:00.000Z',
              receiveStatusDate: '2022-04-15T04:00:00.000Z',
              description: undefined,
              statusLocation: undefined,
            },
          ],
          status: 'status1',
        },
      ]);
    });
  });

  describe('ManifestItemService.getParcelsInHawb', () => {
    it('should query parcels with correct parameters and return result', async () => {
      const mockHawb = {
        merchant_name: 'merchantA',
        mawb_no: 'MAWB123',
      };
      const mockParcels = [{ id: 'p1' }, { id: 'p2' }];

      const findSpy = vi.spyOn(Daos.manifest_items, 'find').mockResolvedValueOnce(mockParcels);

      const result = await ManifestItemService.getParcelsInHawb(mockHawb);

      expect(findSpy).toHaveBeenCalledWith({
        query: `SELECT * from c 
              where c.service_option = 'b2b' and c.merchant_name = @merchantName 
              and c.mawb_no = @mawbNumber`,
        parameters: [
          { name: '@merchantName', value: mockHawb.merchant_name },
          { name: '@mawbNumber', value: mockHawb.mawb_no },
        ],
      });
      expect(result).toEqual(mockParcels);
    });

    it('should return empty array if no parcels found', async () => {
      const mockHawb = {
        merchant_name: 'merchantA',
        mawb_no: 'MAWB123',
      };
      vi.spyOn(Daos.manifest_items, 'find').mockResolvedValueOnce([]);

      const result = await ManifestItemService.getParcelsInHawb(mockHawb);

      expect(result).toEqual([]);
    });
  });

  describe('test getManifestItemsByTrackingIDs', () => {
    test('Daos.manifest_items.find should call correct object when queryBothTIDAndID is true', async () => {
      // Arrange
      const trackingIDs = ['PXL123'];
      const fields = ['id', 'tracking_id'];
      const queryBothTIDAndID = true;
      const expectedCalledObj = {
        parameters: [
          {
            name: '@trackingIds',
            value: ['PXL123'],
          },
        ],
        query:
          'SELECT c.id, c.tracking_id FROM c WHERE ARRAY_CONTAINS(@trackingIds, c.tracking_id) OR ARRAY_CONTAINS(@trackingIds, c.id)',
      };

      // Act
      await ManifestItemService.getManifestItemsByTrackingIDs(
        trackingIDs,
        fields,
        queryBothTIDAndID,
      );

      // Assert
      expect(Daos.manifest_items.find).toHaveBeenCalledWith(expectedCalledObj);
    });
  });
});

describe('test getManifestItemsFromMultipleGaylords func', () => {
  test('it should return correct data', async () => {
    // Arrange
    const gaylordIds = ['GL-22-00000066'];
    const resultMock = 'result';
    Daos.manifest_items.find = vi.fn().mockResolvedValue(resultMock);
    // Act
    const data = await ManifestItemService.getManifestItemsFromMultipleGaylords(gaylordIds);
    // Assert
    expect(data).toBe(resultMock);
  });
});

describe('test function separateItem', () => {
  const rate = {
    destination_currency: 1.5,
  };
  const parcel = {
    item: [
      {
        total_declared_value: 1.6,
      },
      {
        total_declared_value: 2.5,
      },
    ],
    lmd: 'kerry hong kong',
    weight_unit: 'kg',
    weight: 1.5,
    dimensions_unit: 'cm',
    length: 60,
    width: 40,
    height: 20,
  };
  const destination = {
    threshold_check: false,
  };
  const thresholdObj = {
    threshold: 100,
  };
  test('case kerry hong kong', () => {
    // Act
    const data = ManifestItemService.separateItem(rate, parcel, destination, thresholdObj);

    // Assert
    expect(data).toEqual({
      converted_merchant_declared_value: '6.15',
      isBelow: false,
    });
  });

  test('case kerry but not hong kong + not below', () => {
    // Arrange
    parcel.lmd = 'kerry';

    // Act
    const data = ManifestItemService.separateItem(rate, parcel, destination, thresholdObj);

    // Assert
    expect(data).toEqual({
      converted_merchant_declared_value: 201.21,
      isBelow: false,
    });
  });

  test('case kerry but not hong kong + below', () => {
    // Arrange
    parcel.lmd = 'kerry';
    destination.threshold_check = true;

    // Act
    const data = ManifestItemService.separateItem(rate, parcel, destination, thresholdObj);

    // Assert
    expect(data).toEqual({
      converted_merchant_declared_value: 201.21,
      isBelow: true,
    });
  });

  test('case non kerry + below', () => {
    // Arrange
    parcel.lmd = 'ninjavan';
    destination.threshold_check = true;

    // Act
    const data = ManifestItemService.separateItem(rate, parcel, destination, thresholdObj);

    // Assert
    expect(data).toEqual({
      converted_merchant_declared_value: '6.15',
      isBelow: true,
    });
  });

  test('case non kerry + not below', () => {
    // Arrange
    parcel.item = [
      {
        total_declared_value: 60,
      },
      {
        total_declared_value: 25,
      },
    ];
    parcel.lmd = 'ninjavan';
    destination.threshold_check = true;

    // Act
    const data = ManifestItemService.separateItem(rate, parcel, destination, thresholdObj);

    // Assert
    expect(data).toEqual({
      converted_merchant_declared_value: '127.50',
      isBelow: false,
    });
  });
});

describe('it should test isProcessRejectedBooking()', () => {
  test('should return false', async () => {
    // Arrange
    const data = {
      id: '1234',
      tracking_id: '1234',
      tracking_status: [
        {
          status: StatusMappingService.getManifestStatus(ENUM.parcelStatus.booked),
          date: new Date().toISOString(),
        },
        {
          status: StatusMappingService.getManifestStatus(ENUM.parcelStatus.lmd_receive_booking),
          date: new Date().toISOString(),
        },
        {
          status: StatusMappingService.getManifestStatus(ENUM.parcelStatus.lmd_reject_booking),
          date: new Date().toISOString(),
        },
      ],
    };
    // Act
    const result = ManifestItemService.isProcessRejectedBooking(data);
    // Assert
    expect(result).toEqual(false);
  });

  test('should return true', () => {
    // Arrange
    const data = {
      id: '1234',
      tracking_id: '1234',
      tracking_status: [
        {
          status: 'booked',
          date: new Date().toISOString(),
        },
        {
          status: undefined,
          date: new Date().toISOString(),
        },
        {
          status: 'lmd_reject_booking',
          date: new Date().toISOString(),
        },
      ],
    };
    // Act
    const result = ManifestItemService.isProcessRejectedBooking(data);
    // Assert
    expect(result).toEqual(true);
  });
});

describe('test generateAndSendmailCBpart2MY function', () => {
  test('happy case generate file and send email', async () => {
    // Arrange
    const mawb = { tracking_status: [{}] };
    const gaylords = [];
    const allParcels = [{ id: '123' }, { id: '234' }];
    const parcelClassificationData = {
      allParcels: [{ id: '123' }, { id: '234' }],
      parcelsAbove: [{ id: '123' }],
      parcelsBelow: [{ id: '234' }],
    };
    const allOpHubs = [];
    const allConversionRates = [];
    const customBrokerName = '';
    const allMerchants = [];
    const zoneName = '';
    const customBroker = {};
    const req = {};
    ManifestItemService.generateCustomManifest = vi.fn().mockResolvedValue({
      lmd: {
        airport_code: 'SIN',
        lmd_provider_name: 'ninjavan',
      },
      archive: [],
      local_sta: '',
      local_std: '',
      flight_no: '',
      mawbPdfData: {
        mawb_buffer: Buffer.from('test buffer'),
        chargeableWeight: '',
        success: true,
      },
    });
    ManifestItemService.generatePdfProformaInvoice = vi.fn().mockResolvedValue({
      success: true,
      buffer: JSON.stringify(Buffer.from('test buffer')),
    });
    ArchiverService.zip = vi.fn().mockResolvedValueOnce(Buffer.from('filecontent', 'utf-8'));
    AzureBlobStorage.uploadFile = vi.fn().mockResolvedValueOnce('OK');
    EmailService.sendPart2Email = vi.fn().mockResolvedValueOnce(null);
    // Act
    await ManifestItemService.generateAndSendmailCBpart2MY(
      mawb,
      gaylords,
      parcelClassificationData,
      allOpHubs,
      allConversionRates,
      customBrokerName,
      allMerchants,
      zoneName,
      customBroker,
      req,
      allParcels,
    );
    // Assert
    expect(EmailService.sendPart2Email).toHaveBeenCalled();
  });

  describe('getParcelDamagedPic', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });
    const mockBlobs = [
      new Blob(['img1'], { type: 'application/img' }),
      new Blob(['img2'], { type: 'application/img' }),
    ];

    it('should return undefined when no parcel record is found', async () => {
      Daos.manifest_items.find = vi.fn().mockResolvedValue([]);

      const result = await ManifestItemService.getParcelDamagedPic('some-id');

      expect(result).toBeUndefined();
      expect(Daos.manifest_items.find).toHaveBeenCalledWith({
        query: `SELECT c.id, c.tracking_id FROM c WHERE c.id = @id OR c.tracking_id = @id`,
        parameters: [{ name: '@id', value: 'some-id' }],
      });
    });

    it('should return the first image when both images are available', async () => {
      Daos.manifest_items.find = vi
        .fn()
        .mockResolvedValue([{ id: 'id1', tracking_id: 'trackingId1' }]);
      AzureBlobStorage.getLatestFilesByTimestamp = vi.fn().mockResolvedValue(mockBlobs);
      AzureBlobStorage.downloadBlobBuffer = vi
        .fn()
        .mockResolvedValueOnce(Buffer.from('image1'))
        .mockResolvedValueOnce(Buffer.from('image2'));

      const result = await ManifestItemService.getParcelDamagedPic('some-id');

      expect(result).toEqual(Buffer.from('image1'));
      expect(Daos.manifest_items.find).toHaveBeenCalledWith({
        query: `SELECT c.id, c.tracking_id FROM c WHERE c.id = @id OR c.tracking_id = @id`,
        parameters: [{ name: '@id', value: 'some-id' }],
      });
      expect(AzureBlobStorage.downloadBlobBuffer).toHaveBeenCalledTimes(2);
    });

    it('should return the second image when the first image is not available', async () => {
      Daos.manifest_items.find = vi
        .fn()
        .mockResolvedValue([{ id: 'id1', tracking_id: 'trackingId1' }]);
      AzureBlobStorage.downloadBlobBuffer = vi
        .fn()
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(Buffer.from('image2'));

      const result = await ManifestItemService.getParcelDamagedPic('some-id');

      expect(result).toEqual(Buffer.from('image2'));
      expect(Daos.manifest_items.find).toHaveBeenCalledWith({
        query: `SELECT c.id, c.tracking_id FROM c WHERE c.id = @id OR c.tracking_id = @id`,
        parameters: [{ name: '@id', value: 'some-id' }],
      });
      expect(AzureBlobStorage.downloadBlobBuffer).toHaveBeenCalledTimes(2);
    });

    it('should return undefined when no images are available', async () => {
      Daos.manifest_items.find = vi
        .fn()
        .mockResolvedValue([{ id: 'id1', tracking_id: 'trackingId1' }]);
      AzureBlobStorage.downloadBlobBuffer = vi
        .fn()
        .mockResolvedValueOnce(undefined)
        .mockResolvedValueOnce(undefined);

      const result = await ManifestItemService.getParcelDamagedPic('some-id');

      expect(result).toBeUndefined();
      expect(Daos.manifest_items.find).toHaveBeenCalledWith({
        query: `SELECT c.id, c.tracking_id FROM c WHERE c.id = @id OR c.tracking_id = @id`,
        parameters: [{ name: '@id', value: 'some-id' }],
      });
      expect(AzureBlobStorage.downloadBlobBuffer).toHaveBeenCalledTimes(2);
    });
  });

  describe('getParcelByIdOrTrackingId', () => {
    it('function run successfully', async () => {
      // Arrange
      Daos.manifest_items.find = vi.fn().mockResolvedValueOnce({ id: '123' });

      // Act
      const data = await ManifestItemService.getParcelByIdOrTrackingId('123');

      // Asset
      expect(data).toStrictEqual({ id: '123' });
    });
  });
});

describe('test filterCancellableParcels', () => {
  const parentId = 'parent-id';
  StatusMappingService.getMultipleManifestStatus = vi
    .fn()
    .mockReturnValue(['Cancelled', 'HOLD: Cancelled']);

  it('should return all cancellable child parcels if given one is parent', async () => {
    // Arrange
    const mockReturnedParcels = [{ id: 'parcelId', parent_id: 'parent-id' }];
    const childParcels = [
      ...mockReturnedParcels,
      {
        id: 'randomId',
        parent_id: 'parent-id',
        PD_invoicing_status: [
          {
            status: 'PD ready to invoice',
            timestamp: new Date().toISOString(),
          },
        ],
      },
    ];
    const isGivenParent = true;
    Daos.manifest_items.find = vi.fn().mockResolvedValueOnce(childParcels);
    StatusMappingService.isCancellable = vi.fn().mockReturnValue(true);

    // Act
    const result = await ManifestItemService.filterCancellableParcels(parentId, isGivenParent);

    // Assert
    expect(Daos.manifest_items.find).toHaveBeenCalledOnce();
    expect(result).toEqual({
      parcels: mockReturnedParcels,
      isSplitCompleted: true,
    });
  });

  it('should return parent if given parcel is the last child', async () => {
    // Arrange
    const parentParcel = {
      id: 'parent-id',
      parent_id: 'parent-id',
    };
    const cancellableParcels = [
      parentParcel,
      {
        id: 'parcelId',
        parent_id: 'parent-id',
      },
    ];
    const isGivenParent = false;
    Daos.manifest_items.find = vi.fn().mockResolvedValueOnce(cancellableParcels);
    StatusMappingService.isCancellable = vi.fn().mockReturnValue(true);

    // Act
    const result = await ManifestItemService.filterCancellableParcels(parentId, isGivenParent);

    // Assert
    expect(Daos.manifest_items.find).toHaveBeenCalledOnce();
    expect(result).toEqual({
      parcels: [parentParcel],
      isSplitCompleted: true,
    });
  });

  it('should return empty array if given parcel is not the last child', async () => {
    // Arrange
    const parentParcel = {
      id: 'parent-id',
      parent_id: 'parent-id',
    };
    const cancellableParcels = [
      parentParcel,
      {
        id: 'parcelId',
        parent_id: 'parent-id',
      },
      {
        id: 'parcelId2',
        parent_id: 'parent-id',
      },
    ];
    const isGivenParent = false;
    Daos.manifest_items.find = vi.fn().mockResolvedValueOnce(cancellableParcels);
    StatusMappingService.isCancellable = vi.fn().mockReturnValue(true);

    // Act
    const result = await ManifestItemService.filterCancellableParcels(parentId, isGivenParent);

    // Assert
    expect(Daos.manifest_items.find).toHaveBeenCalledOnce();
    expect(result).toEqual({
      parcels: [],
      isSplitCompleted: false,
    });
  });
});

describe('test getCancellableMultiPieceParcels', () => {
  it('should return empty array if parcel is untagged', async () => {
    // Arrange
    const parcel: { id: string; parent_id?: string } = {
      id: 'parcelId',
    };
    ManifestItemService.filterCancellableParcels = vi.fn();

    // Act
    const result = await ManifestItemService.getCancellableMultiPieceParcels(
      parcel.id,
      parcel.parent_id,
    );

    // Assert
    expect(ManifestItemService.filterCancellableParcels).not.toHaveBeenCalled();
    expect(result).toEqual({
      data: [],
      isSplitCompleted: false,
    });
  });

  it('should return all cancellable child parcels if given one is parent', async () => {
    // Arrange
    const parcel = {
      id: 'parent-id',
      parent_id: 'parent-id',
    };
    const mockCancellableChildParcels = [
      {
        id: 'parcelId',
        parent_id: 'parent-id',
      },
    ];
    ManifestItemService.filterCancellableParcels = vi.fn().mockResolvedValueOnce({
      parcels: mockCancellableChildParcels,
      isSplitCompleted: true,
    });

    // Act
    const result = await ManifestItemService.getCancellableMultiPieceParcels(
      parcel.id,
      parcel.parent_id,
    );

    // Assert
    expect(ManifestItemService.filterCancellableParcels).toHaveBeenCalledOnce();
    expect(result).toEqual({
      data: mockCancellableChildParcels,
      isParent: true,
      isSplitCompleted: true,
    });
  });

  it('should return parent if given parcel is the last child', async () => {
    // Arrange
    const parcel = {
      id: 'parcelId',
      parent_id: 'parent-id',
    };
    const mockParentParcel = {
      id: 'parent-id',
      parent_id: 'parent-id',
    };
    ManifestItemService.filterCancellableParcels = vi.fn().mockResolvedValueOnce({
      parcels: mockParentParcel,
      isSplitCompleted: true,
    });

    // Act
    const result = await ManifestItemService.getCancellableMultiPieceParcels(
      parcel.id,
      parcel.parent_id,
    );

    // Assert
    expect(ManifestItemService.filterCancellableParcels).toHaveBeenCalledOnce();
    expect(result).toEqual({
      data: mockParentParcel,
      isSplitCompleted: true,
    });
  });

  it('should return empty if given parcel is not the last child', async () => {
    // Arrange
    const parcel = {
      id: 'parcelId',
      parent_id: 'parent-id',
    };
    ManifestItemService.filterCancellableParcels = vi.fn().mockResolvedValueOnce({
      parcels: [],
      isSplitCompleted: false,
    });

    // Act
    const result = await ManifestItemService.getCancellableMultiPieceParcels(
      parcel.id,
      parcel.parent_id,
    );

    // Assert
    expect(ManifestItemService.filterCancellableParcels).toHaveBeenCalledOnce();
    expect(result).toEqual({
      data: [],
      isSplitCompleted: false,
    });
  });

  it('should handle exception and return empty array', async () => {
    // Arrange
    const parcel = {
      id: 'parcelId',
      parent_id: 'parent-id',
    };
    ManifestItemService.filterCancellableParcels = vi
      .fn()
      .mockRejectedValueOnce('Unexpected error');

    // Act
    const result = await ManifestItemService.getCancellableMultiPieceParcels(
      parcel.id,
      parcel.parent_id,
    );

    // Assert
    expect(ManifestItemService.filterCancellableParcels).toHaveBeenCalledOnce();
    expect(result).toEqual({
      data: [],
      isSplitCompleted: false,
    });
  });
});

describe('test getCancellableParcelsByParentId should return all cancellable parcels', () => {
  it('should return payload to in which isSplitCompleted is false', async () => {
    // Arrange
    const parentId = 'parentId';
    const expected = [
      {
        id: 'parcelId',
        parent_id: 'parentId',
        latest_tracking_status: 'Booked',
        lmd: 'LMD',
      },
    ];
    StatusMappingService.getManifestStatus = vi.fn().mockReturnValueOnce('Cancelled');
    Daos.manifest_items.find = vi.fn().mockResolvedValueOnce(expected);

    // Act
    const result = await ManifestItemService.getCancellableParcelsByParentId(parentId);

    // Assert
    expect(Daos.manifest_items.find).toHaveBeenCalledOnce();
    expect(result).toEqual({
      data: expected,
      isSplitCompleted: false,
    });
  });

  it('should return all cancellable parcels including the parent', async () => {
    // Arrange
    const parentId = 'parentId';
    const expected = [
      {
        id: 'parcelId',
        parent_id: 'parentId',
        latest_tracking_status: 'Booked',
        lmd: 'LMD',
      },
      {
        id: 'parcelId-2',
        parent_id: 'parentId',
        latest_tracking_status: 'LMD reveive booking',
        lmd: 'LMD',
        PD_invoicing_status: [
          {
            status: 'PD ready to invoice',
            date: new Date().toISOString(),
          },
        ],
      },
    ];
    StatusMappingService.getManifestStatus = vi.fn().mockReturnValueOnce('Cancelled');
    Daos.manifest_items.find = vi.fn().mockResolvedValueOnce(expected);

    // Act
    const result = await ManifestItemService.getCancellableParcelsByParentId(parentId);

    // Assert
    expect(Daos.manifest_items.find).toHaveBeenCalledOnce();
    expect(result).toEqual({
      data: [expected[0]],
      isSplitCompleted: true,
    });
  });
});
